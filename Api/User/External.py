# /bin/env python3
# -*- coding: UTF-8 -*-

"""
功能描述：用户数据记录模块
编写人：darkedge
编写日期：2021年12月31日
"""

# trunk-ignore(ruff/F401)
import datetime
# trunk-ignore(ruff/F401)
import random
import time
import json

from Api.User.Base import UserBase<PERSON><PERSON>, domain
from Model.Kingdom import Kingdom
# trunk-ignore(ruff/F401)
from Model.UserError import UserError
from Unit.Redis import redisHelper

domainNew = domain


class UserExternal(UserBaseApi):
    def info(self, toLoc, isGrid=False, rallyMoId=None):
        """指定坐标信息"""
        if isinstance(toLoc,str):
            return self.log(toLoc)
        if self.isInvalid:
            return None

        loc = None
        if len(toLoc) == 3:
            loc = toLoc
        else:
            loc = [self.worldId] + toLoc

        data = {"fromId": self.fieldObjectId, "toLoc": loc}
        if rallyMoId:
            data["rallyMoId"] = rallyMoId
        # if self.level != 30:
        #     self.randomSleep(1,2)
        res = self.request(domain + "api/field/march/info", data, useLock=False)
        if res and res.get("result"):
            # 军队信息
            troops = res.get("troops")
            self.kingdomTroops = troops
            # marchType = res.get("marchType")
            fo = res.get("fo")
            if fo:
                originLoc = fo.get("originLoc")
                if originLoc:
                    return self.info(originLoc[1:])
            return res
        elif res:
            err = res.get("err")
            if err:
                code = err.get("code")
                if code:
                    if code == "no_fieldobject":
                        # 无效坐标
                        # 添加到redis
                        if isGrid:
                            redisHelper.addLoc(loc)
                        return None
                    elif code == "cannot_gathering":
                        return None

        self.log("异常的info %s" % res)
        return None

    # 侦查
    def startInvestigation(self, toLoc, retry=0):
        if self.isInvalid:
            return None

        if len(toLoc) == 2:
            loc = [self.worldId] + toLoc
            return self.startInvestigation(loc)

        if retry > 4:
            self.errorLog("侦查失败,异常的坐标/位置")
            return None

        data = {
            "fromId": self.fieldObjectId,
            "toLoc": toLoc,
            "marchTroops": [],
            "marchType": 6,
        }
        res = self.request(domainNew + "api/field/march/start", data, isB64=True)
        if res and res.get("result"):
            return res
        elif res:
            err = res.get("err")
            if err:
                code = err.get("code")
                if code:
                    if code == "different_world":
                        self.randomSleep(5, 10)
                        return self.startInvestigation(toLoc, retry + 1)
                    #     # 无效坐标
                    #     return None
                    elif code == "no_field_object":

                        return None
                    elif code == "shrine_not_enemy":
                        self.randomSleep(120, 240, msg="自己的神殿")
                        return None
                    elif code == "not_open_cvc":
                        self.cvcEventOpen = False
            else:
                self.randomSleep(5, 10)
                return self.startInvestigation(toLoc, retry + 1)

        return res

    def startGarrison(self, toLoc, marchTroops):
        """驻防"""

        if len(toLoc) == 2:
            loc = [self.worldId] + toLoc
            return self.startGarrison(loc, marchTroops)

        res = self.requestStart(7, toLoc, marchTroops)
        if res and res.get("result"):
            return True
        elif res and res.get("err"):
            err = res.get("err")
            if err.get("code") in ["different_world","shrine_enemy"]:
                self.log("驻防失败，被抢走了")
                # self.randomSleep(15, 30)
        return False

    def requestStart(self, marchType, toLoc, marchTroops, dragoId=None, useLock=True):
        """
        出兵接口
        """
        if self.isInvalid:
            return None

        data = {
            "fromId": self.fieldObjectId,
            "toLoc": toLoc,
            "marchTroops": marchTroops,
            "marchType": marchType,
        }
        if dragoId:
            data["dragoId"] = dragoId

        res = self.request(
            domainNew + "api/field/march/start", data, useLock=useLock, isB64=True
        )
        # if res and res.get("result") and marchType == 1:
        #     self.info(toLoc[1:])
        return res

    def startAttack(self, toLoc, marchTroops, name=None):
        """攻击王国"""
        if self.isInvalid:
            return False

        if self.checkFrequentKingdom(Kingdom(name, toLoc)):
            return False

        res = self.requestStart(2, [self.worldId] + toLoc, marchTroops)
        if res and res.get("result"):
            self.debuglog("攻击 %s 坐标 %s 成功" % (name, toLoc))
            self.dataRecord().updateAttackCount(1).commit()
            return True
        else:
            if res and res.get("err"):
                code = res.get("err").get("code")
                if code:
                    if code == "attack_kingdom_too_many":
                        self.log("攻击太频繁 王国:%s" % name)
                        self.frequentKingdoms[name] = time.time()
                        return False
                    if code == "in_shield":
                        self.log("王国:%s 开盾了" % name)
                        self.frequentKingdoms[name] = time.time()
                        return False
            self.errorLog("攻击失败 %s 原因:%s" % (toLoc, res))
            return False

    # 获取世界地图信息(没卵用)
    def worldmapObjects(self, worldId):
        if self.isInvalid:
            return None
        data = {
            "worldId": worldId,
        }
        res = self.request(domainNew + "api/field/worldmap/objects", data)
        if res and res.get("result"):
            return res.get("objects")
        return None

    # 世界列表
    def worldList(self):
        if self.isInvalid:
            return None
        res = self.request(domainNew + "api/kingdom/world/list")
        if res and res.get("result"):
            return res
        return None

    # api/alliance/battle/list
    def allianceBattleList(self):
        """团战信息"""
        if self.isInvalid:
            return None

        if not self.allianceId:
            self.errorLog("没有联盟!不能请求团战信息!")
            return None

        res = self.request(domainNew + "api/alliance/battle/list/v2")
        if res and res.get("isPacked"):
            payload = res.get("payload")
            res = self.uint8ArrayToJson(payload, b64=False)
            battles = res.get("battles")
            redisHelper.saveAllianceBattleList(self.allianceId, battles)
            return battles
        elif res and res.get("result"):
            battles = res.get("battles")
            redisHelper.saveAllianceBattleList(self.allianceId, battles)
            return battles
        elif res.get("err"):
            err = res.get("err")
            code = err.get("code", "")
            if code == "no_alliance":
                self.allianceId = None
                self.errorLog("没有联盟allianceBattleList")

        return None

    # api/alliance/battle/info
    def allianceBattleInfo(self, rallyMoId):
        """获取指定团战ID的详细信息,包括参与部队的速度等信息"""
        if self.isInvalid:
            return None

        # 请求团战详情API
        res = self.request(
            domainNew + "api/alliance/battle/info", {"rallyMoId": rallyMoId}
        )
        if res and res.get("result"):
            battle = res.get("battle")
            # 获取参与团战的部队列表
            rallyTroops = battle.get("rallyTroops",[])
            # 如果有多个部队参与
            if rallyTroops and len(rallyTroops) > 1:
                # 初始化最小速度为10
                minSpeed = 10
                # 从Redis获取已保存的团战速度信息
                rallyInfo = redisHelper.getRallySolwInfo(rallyMoId)
                if rallyInfo:
                    minSpeed = rallyInfo.get("speed",minSpeed)
                # 遍历除第一个部队外的所有部队(从index 1开始)
                for rallyTroop in rallyTroops[1:]:
                    mo = rallyTroop.get("mo", {})
                    moDistance = mo.get("distance") # 部队行军距离
                    moTime = mo.get("time")        # 部队行军时间
                    if moDistance and moTime:
                        # 跳过行军时间为300的部队
                        if moTime == 300:
                            continue
                        # 计算部队速度(距离/时间)
                        moSpeed = round(moDistance / moTime, 2)
                        # 如果当前部队速度小于最小速度
                        if moSpeed < minSpeed:
                            minSpeed = moSpeed
                            moKingdom = mo.get("kingdom",{})
                            name = moKingdom.get("name","unknown")
                            # 记录部队信息:名称、速度、距离、时间
                            info = f'{name} {moSpeed} {moDistance} {moTime}'
                            self.rallyMoIdList[rallyMoId] = info
                            # 保存最慢部队信息到Redis
                            redisHelper.setRallySolwInfo(rallyMoId, name, moSpeed)
                # 如果最小速度小于1,记录详细日志
                if minSpeed < 1:
                    self.debuglog(f'joinAllianceBattleLow {rallyMoId} {json.dumps(battle, ensure_ascii=False)}')
                else:
                    try:
                        # 记录团战加入日志
                        self.debuglog(f'joinAllianceBattle {rallyMoId} {self.rallyMoIdList.get(rallyMoId,"???")} {minSpeed}')
                    except Exception:
                        pass

            return battle
        return None

    # api/field/rally/join
    def rallyJoin(self, rallyMoId, marchTroops, useLock=True):
        """加入团战"""
        if self.isInvalid:
            return None
        data = {
            "marchTroops": marchTroops,
            "rallyMoId": rallyMoId,
        }

        res = self.request(domainNew + "api/field/rally/join", data, isB64=True, useLock=useLock)
        self.randomSleep(1,2)
        if res and res.get("result"):
            self.attackCount += 1
            return res
        elif res and res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code == "insufficient_actionpoint":
                return False
            return res

        return None

    # api/field/rally/start 5打野
    def rallyStart(
        self, marchType, toLoc, marchTroops, rallyTime=5, message="自动开团 只进同兵种"
    ):
        """开始团战"""
        if self.isInvalid:
            return None
        # self.log(f'开始团战 {message}, 坐标 {toLoc}, 兵种 {marchTroops},{marchType}')
        # return {"result":True}
        data = {
            # "fromId":self.fieldObjectId,
            "marchTroops": marchTroops,
            "toLoc": toLoc,
            "marchType": marchType,
            "rallyTime": rallyTime,
            "message": message,
        }
        res = self.request(domainNew + "api/field/rally/start", data, isB64=True)
        return res

    # api/field/rally/dismiss
    def rallyDissmiss(self, rallyMoId, moId):
        """撤回部队"""
        if self.isInvalid:
            return None
        data = {
            "rallyMoId": rallyMoId,
            "moId": moId,
        }

        res = self.request(domainNew + "api/field/rally/dismiss", data)
        if res.get("result"):
            return True
        else:
            pass

        return False

    # api/kingdom/check/immigration
    def checkImmigration(self, worldId):
        """确认移民"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/kingdom/check/immigration", {"worldId": worldId}
        )
        if res and res.get("result"):
            return res
        return None

    def worldImmigration(self, worldId):
        """移民"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/kingdom/world/immigration", {"worldId": worldId}
        )
        if res and res.get("result"):
            fo = res.get("fo")
            if fo and fo.get("loc"):
                self.loc = fo.get("loc")
            return res
        elif res and res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code == "same_world":
                self.loc = [worldId, 1, 1]
                return True
        return None

    def shrineSupportReturn(self, moId, isNeedKingdomId=True):
        """神殿支援返回"""
        if self.isInvalid:
            return None
        data = {
            "moId": moId,
        }
        if isNeedKingdomId:
            data["kingdomId"] = self.kingdomId

        res = self.request(domainNew + "api/alliance/shrine/support/return", data)
        if res and res.get("result"):
            return res
        return None

    def fieldMarchReturn(self, moId, needKingdomId=True):
        """返回"""
        if self.isInvalid:
            return None

        data = {
            "moId": moId,
        }
        if needKingdomId:
            data["kingdomId"] = self.kingdomId

        res = self.request(domainNew + "api/field/march/return", data, isB64=True)
        if res and res.get("result"):
            return res
        return None

    # api/kingdom/support/troops/return
    def kingdomSupportReturn(self, moId, useLock = True):
        """城堡支援返回"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/kingdom/support/troops/return", {"moId": moId},
            useLock= useLock
        )
        if res and res.get("result"):
            return res
        return None

    # api/alarm/list
    def alarmList(self):
        """报警列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alarm/list")
        if res and res.get("result"):
            return res.get("alarms")
        return None

    # api/field/march/boost
    def fieldMarchBoost(self, moId):
        """水晶加速"""
        if self.isInvalid:
            return None

        data = {
            "kingdomId": self.kingdomId,
            "moId": moId,
        }
        res = self.request(domainNew + "api/field/march/boost", data, useLock=False)
        if res and res.get("result"):
            return True
        return False

    # api/event/cvc/enter
    def cvcEnter(self, useLock = True):
        """进入CVC"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/event/cvc/enter", useLock = useLock)
        if res and res.get("result"):
            self.addBotWorkLog("进入CVC")
            return res
        return False

    # api/event/cvc/leave
    def cvcLeave(self, useLock = True):
        """离开CVC"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/event/cvc/leave", useLock = useLock)
        if res and res.get("result"):
            self.addBotWorkLog("离开CVC")
            return res
        return False

    # api/field/shrine/info
    def shrineInfo(self, foId):
        """神殿信息"""
        """
        {
            "kingdomId": "61c00a9922b56d1541efe9f4",
            "moId": "6266a8cbb5518b1f924fb4fe",
            "troops": [
                {
                "code": 50100305,
                "level": 0,
                "select": 0,
                "amount": 2,
                "dead": 0,
                "wounded": 0,
                "seq": 0
                }
            ],
            "status": 1
        }
        """
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/field/shrine/info", {"foId": foId})
        if res and res.get("result"):
            return res.get("shrine")
        return None

    # api/alliance/shrine/list
    def shrineList(self):
        """神殿列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/alliance/shrine/list")
        if res and res.get("result"):
            return res.get("shrines")
        return None

    # api/alliance/shrine/support/list
    def shrineSupportList(self, foId):
        """神殿支援列表"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/alliance/shrine/support/list",
            {"foId": foId},
            useLock=False,
            timeout=5,
        )
        if res and res.get("result"):
            return res
        elif res.get("err"):
            err = res.get("err")
            code = err.get("code")
            if code == "not_in_alliance":
                self.tryJoinAllianceBySystem()
        return None

    # api/field/building/supports
    def buildingSupports(self, foId):
        """建筑支援列表"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/field/building/supports", {"foId": foId})
        if res and res.get("result"):
            return res
        return None

    # api/alliance/shrine/support/setleader
    def shrineSupportSetLeader(self, foId, moId):
        """设置支援队长"""

        if self.isInvalid:
            return None

        data = {
            "foId": foId,
            "moId": moId,
        }
        res = self.request(domainNew + "api/alliance/shrine/support/setleader", data)
        if res and res.get("result"):
            return res
        return None

    def avaiableAllianceBattleList(self):
        """可用团战列表"""
        battles = []

        if self.isInvalid:
            return None

        res = self.allianceBattleList()
        if res:
            for battle in res:
                if battle.get("isRally") is True:
                    if self.loadBattleInfo(battle):
                        battles.append(battle)
        return battles

    def tryReturnShrineSupportList(self, foId, gobackLess=False, leaderName=None):
        res = self.shrineSupportList(foId)
        if res:
            troops = res.get("troops")
            spyCount = 0
            isDiffWorld = False
            if troops:
                topTroop = troops[0]
                needLeader = False
                if leaderName:
                    needLeader = self.checkTroopInfoEqual(topTroop, leaderName)
                    if needLeader:
                        self.log("当前需要变更领导者")

                for supportTroop in troops:
                    moId = supportTroop.get("moId")
                    mo = supportTroop.get("mo")
                    currentTroops = supportTroop.get("troops")
                    kingdom = mo.get("kingdom")
                    worldId = kingdom.get("worldId")
                    name = kingdom.get("name")
                    status = supportTroop.get("status")
                    # self.log(f'worldId:{worldId}')
                    if worldId != self.realWorld:
                        isDiffWorld = True
                        self.log(f"敌人 {name}")
                        continue

                    if leaderName:
                        if leaderName == name:
                            if needLeader and status == 1:
                                self.shrineSupportSetLeader(foId, moId)
                                self.log(f"变更领导者:{name}")
                            continue

                    for troop in currentTroops:
                        code = troop.get("code")
                        if code % 10 < 4:
                            spyCount += 1
                            self.log(f"发现间谍弟弟 {worldId} {name} T{code%10}")
                            self.shrineSupportReturn(moId, False)
                            break
                        if gobackLess:
                            amount = troop.get("amount")
                            if amount % 100 > 0 and amount < 300000:
                                self.log(
                                    f"发现少兵 {worldId} {name} T{code%10} {amount}"
                                )
                                self.shrineSupportReturn(moId, False)
                                break
                self.log(f"发现间谍弟弟 {spyCount}")
                if isDiffWorld:
                    self.debuglog("发现异大陆兵团,被抢了?")
                    return False
                return True
        else:
            self.log("没有有效的队列")

        return False

    def monitoringShrineSupportList(self, foId, showAll=False):
        """监控神殿支援列表"""
        if self.isInvalid:
            return None

        res = self.shrineSupportList(foId)
        showAllList = []
        arms = [0, 0, 0]
        if res:
            troops = res.get("troops")
            names = ["步兵", "弓兵", "骑兵"]
            firstName = None
            realFirstName = None
            if troops:
                troopMap = {}
                troopRunMap = {}
                worldId = 0
                isReal = False

                for supportTroop in troops:
                    moDetail = {}
                    currentTroops = supportTroop.get("troops")
                    status = int(supportTroop.get("status"))
                    mo = supportTroop.get("mo")
                    moDetail["id"] = mo.get("_id")
                    moDetail["status"] = status
                    currentTroops = supportTroop.get("troops")
                    kingdom = mo.get("kingdom")
                    lord = kingdom.get("lord")
                    lordInt = int(lord.get("level"))
                    worldId = kingdom.get("worldId")
                    moDetail["name"] = kingdom.get("name")
                    moDetail["worldId"] = worldId
                    moDetail["lord"] = lordInt
                    moDetail["kingdomId"] = kingdom["_id"]

                    if not isReal and status == 1:
                        isReal = True
                        if firstName:
                            realFirstName = self.loadLeadInfo(
                                worldId, currentTroops, status, mo, kingdom, lordInt
                            )

                    if firstName is None:
                        firstName = self.loadLeadInfo(
                            worldId, currentTroops, status, mo, kingdom, lordInt
                        )
                        # self.debuglog(supportTroop)
                    # if status != 1:
                    #     continue
                    for troop in currentTroops:
                        code = troop.get("code")
                        name = names[code // 100 % 10 - 1]
                        level = code % 10
                        amount = troop.get("amount")
                        key = f"{name}T{level}"
                        writeMap = troopMap
                        if status != 1:
                            writeMap = troopRunMap

                        if writeMap.get(key):
                            writeMap[key] += amount
                        else:
                            writeMap[key] = amount

                        troopList = moDetail.get("troopList", [])
                        troopList.append(f"{key} {amount}")
                        moDetail["troopList"] = troopList
                        if not showAll:
                            index = names.index(name)
                            arms[index] += amount

                    if showAll:
                        showAllList.append(moDetail)

                troopStr = f"{firstName}"
                if realFirstName:
                    troopStr += f"\n真实:{realFirstName}"
                sum = 0
                for key in troopMap:
                    sum += troopMap[key]
                if sum > 0:
                    sumStr = f"{sum // 10000}万"
                    if sum < 100_0000:
                        sumStr = self.redString(sumStr)
                    troopStr += f"\n驻守总兵力:{sumStr}"
                    for key in troopMap:
                        value = troopMap[key]
                        if value > 0:
                            troopStr += f"\n{key}:{value} {round(value/sum * 100,2)}%"

                if troopRunMap:
                    sum = 0
                    for key in troopRunMap:
                        sum += troopRunMap[key]
                    if sum > 0:
                        troopStr += f"\n\n\n行军中总兵力:{sum // 10000}万"
                        for key in troopRunMap:
                            value = troopRunMap[key]
                            if value > 0:
                                troopStr += (
                                    f"\n{key}:{value} {round(value/sum * 100,2)}%"
                                )

                if not showAll:
                    showAllList = arms
                return troopStr, showAllList
            else:
                self.log("没有驻兵")

        else:
            self.log("没有有效数据")
        return "", None

    def loadLeadInfo(self, worldId, currentTroops, status, mo, kingdom, lordInt):
        fromLoc = mo.get("fromLoc")
        firstTroops = [troop.get("amount") for troop in currentTroops]
        firstSum = 0
        for v in firstTroops:
            firstSum += int(v)
        leaderSumStr = ""
        if firstSum > 10000:
            firstSum = round(firstSum / 10000, 2)
            leaderSumStr = f"{firstSum}万"
        else:
            leaderSumStr = f"{firstSum} {firstTroops}"
        statusCN = status != 1 and self.redString(f"异常{status}") or ""
        lordCN = lordInt < 40 and self.redString(f"{lordInt}") or f"{lordInt}"
        firstName = f'占领方{worldId} 指挥官 {kingdom.get("name")} 主{lordCN} 战力 {round(kingdom.get("power",0) //10000 / 10000,2)}E {statusCN} \n指挥官兵力:{leaderSumStr} 坐标{fromLoc[1]},{fromLoc[2]}'
        return firstName

    def monitoringAllianceBattle(self, realWorld=None):
        """检测团战数据"""
        if self.isInvalid:
            return None

        if realWorld is None:
            realWorld = self.realWorld
        battles = []
        res = self.allianceBattleList()
        if res:
            for battle in res:
                if battle.get("isRally") is True:
                    if self.loadBattleInfo(battle):
                        targetWorldId = battle.get("targetWorldId")
                        if not targetWorldId:
                            allianceId = battle.get("allianceId")
                            if self.allianceId and self.allianceId == allianceId:
                                continue
                        numTroops = battle.get("numTroops")
                        leaderKingdomWorldId = battle.get("leaderKingdomWorldId")
                        if targetWorldId and targetWorldId != realWorld:
                            maxTroops = battle.get("maxTroops")
                            if leaderKingdomWorldId != realWorld:
                                continue
                            if redisHelper.getSelfRallyEffective(battle["_id"]):
                                continue

                        if numTroops < 500000:
                            continue
                        battle = self.monitoringAllianceBattleOnce(
                            rallyMoId=battle["_id"], battle=battle
                        )
                        if leaderKingdomWorldId == realWorld:
                            if battle.get("onlyTroop"):
                                if maxTroops == numTroops:
                                    redisHelper.setSelfRallyEffective(battle["_id"])
                                continue
                        battles.append(battle)

        return battles

    def monitoringAllianceBattleOnce(self, rallyMoId, battle=None):
        """检测单次团战"""
        battleDetail = self.allianceBattleInfo(rallyMoId=rallyMoId)
        if battleDetail is None:
            return None
        self.loadBattleDetailInfo(battleDetail)
        if battle:
            battle["troopMap"] = battleDetail.get("troopMap")
            battle["troopRunMap"] = battleDetail.get("troopRunMap")

            if len(battle["troopRunMap"]) == 1:
                battle["onlyTroop"] = True
            overTime = self.compareDateTimeWithNow(
                battle["endTime"] or battleDetail["endTime"]
            )
            battle["overTime"] = int(overTime)
            return battle
        else:
            overTime = self.compareDateTimeWithNow(battleDetail["endTime"])
            battleDetail["overTime"] = int(overTime)
        return battleDetail

    # api/kingdom/report
    def kingdomReport(self, targetKingdomId, reports=None):
        """举报用户"""

        if self.isInvalid:
            return None

        if reports is None:
            reports = [2]

        data = {"targetKingdomId": targetKingdomId, "reports": reports}

        res = self.request(domainNew + "api/kingdom/report", data)
        if res and res.get("result"):
            return True
        return False

    # api/kingdom/profile/ranking
    def rankingDashboard(self, type=5):
        """排行榜"""
        if self.isInvalid:
            return None

        res = self.request(domainNew + "api/kingdom/profile/ranking", {"type": type})
        if res and res.get("result"):
            return res.get("rankings")
        return None

    # api/kingdom/profile/other
    def kingdomProfileOther(self, kingdomId):
        """其他用户"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/kingdom/profile/other", {"kingdomId": kingdomId}
        )
        if res and res.get("result"):
            return res
        return None

    # api/mail/send
    def mailSend(self, toName, subject, content=""):
        """发邮件"""
        if self.isInvalid:
            return None

        res = self.request(
            domainNew + "api/mail/send",
            {"toName": toName, "subject": subject, "content": content},
        )
        if res and res.get("result"):
            return True
        return False

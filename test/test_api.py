# trunk-ignore-all(ruff/F401)

import asyncio
import json
import os
import random
import resource
import sys
import threading
import time
import unittest
import zlib
from concurrent.futures import ThreadPoolExecutor

from tqdm import tqdm

import Unit.enum as enum
from Api.FlaskHelper import requestToken, setEnvUrl
from Api.Ipinfo import getIP, getIP3
from Api.User.WebSocket import fieldNames
from Api.UserInfo import Enum, UserInfo, freeBuildJson, loadBuildJson
from Celery.task import collectCrystalsTask
from Model.Account import Account
from Model.UserError import UserError
from Unit.FileTool import loadS5List
from Unit.LandZoneUnit import (
    MaxSixThreadZoneSize,
    crystalLands,
    returnAdjacentCrystalLands,
)
from Unit.Logger import logger
from Unit.Redis import redisHelper, todayStr, tzDate


def memory_usage():
    # 获取当前进程的内存使用情况
    usage = resource.getrusage(resource.RUSAGE_SELF).ru_maxrss
    if sys.platform == "darwin":
        # 在 macOS 上，ru_maxrss 单位为字节，需要转换为千字节
        return usage / 1024
    else:
        # 在 Linux 上，ru_maxrss 单位为千字节
        return usage


async def asyncA(n):
    logger.info(f"开始执行 {n}")
    if n % 2 == 0:
        await asyncio.sleep(2)
    logger.info(f"结束执行 {n}")
    return n


async def asyncB(u1: UserInfo):
    await u1.vipInfo()


class UnitTest(unittest.TestCase):
    def setUp(self):
        pass

    def setupAccount(self, userKey=None, token=None) -> UserInfo:
        user = None
        if userKey:
            user = UserInfo(
                userKey=userKey, socks5=random.choice(loadS5List()), saveSocks5=True
            )
            user.token = token
        else:
            user = UserInfo(
                token=token, socks5=random.choice(loadS5List()), saveSocks5=True
            )
            user.loadEmailWithToken()
        if not user.token:
            user.token = user.loadRedisToken()
        user.initLog(needTestS5=False)
        return user

    def loadGuestAccount(self):
        accounts = []
        with open("./guestaccount.txt", "r") as f:
            for line in f.readlines():
                if line.strip():
                    accounts.append(line.strip())
        users = [self.setupAccount(v) for v in accounts]
        return users

    def buildTokenAccount(self, token, socks5=None):
        user = UserInfo(token=token, socks5=socks5)
        user.loadEmailWithToken()
        user.initLog(needTestS5=False)
        return user

    def buildUserKeyAccount(self, userKey, socks5=None):
        user = UserInfo(userKey=userKey, socks5=socks5)
        user.token = user.loadRedisToken()
        user.initLog(needTestS5=False)
        return user

    def test_taskAll(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PMBT7diUzXmCKjcKVPq83zVq6JdLFrPPp-yDgeBB3Oc"
        user = self.buildTokenAccount(token, socks5="127.0.0.1:30001")
        print(json.dumps(user.taskAll(), ensure_ascii=False))

    def test_gamews(self):
        user: UserInfo = None
        user = self.buildUserKeyAccount("029a0457-8417-c379-0d2a-faa44b2835d7")
        user.loc = [20, 1024, 1024]
        user.debugToConsole()
        user.token = user.loadRedisToken()
        user.initLog(needTestS5=False)
        if user.login():
            user.wsWithKingdomApp(isDebug=True)
            user.wsWithMatch3App(isDebug=True)
            time.sleep(2)
            while not user.checkWSWithMatch3App():
                time.sleep(10)
            user.log("结束")

    def test_game(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.KkXoOMGll81KIlrPXmDQUwWX3hNSdMldFtBME4y_Kc8"
        user = self.buildTokenAccount(token)
        user.level = 35
        user.loc = [20, 1024, 1024]
        user.debugToConsole()
        user.wsWithMatch3App(isDebug=True)
        time.sleep(2)
        while not user.checkWSWithMatch3App():
            time.sleep(10)
        user.log("结束")

    def test_tryDeleteResourceMail(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.8hJhZJX3V__tuol6uLvI_c89ZWUeoFX6r5jQUOTqBXs"
        user = self.buildTokenAccount(token, socks5="127.0.0.1:1087")
        user.debugToConsole()
        user.tryDeleteResourceMail()

    def test_gamewsself(self):
        user: UserInfo = None
        # user = UserInfo(userKey='029a0457-8417-c379-0d2a-faa44b2835d1')
        user = UserInfo("<EMAIL>")
        user.loc = [20, 1024, 1024]
        user.level = 30
        user.debugToConsole()
        user.token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PxBVTy91g91kDX8-7H-tW07ctC3gcgG9pEBxWb2mj6I"
        user.initLog(needTestS5=False)
        if True or user.login():
            for _ in range(30):
                user.wsWithMatch3App(isDebug=True)
                time.sleep(2)
                while not user.checkWSWithMatch3App():
                    time.sleep(10)
                user.log("结束一轮")
                time.sleep(60 * 30)
            user.log("结束")

    def test_loadCastleDiff(self):
        paths = [
            # "tmpaccount/2023-11-28-**********.txt",
            # "tmpaccount/2024-08-09-**********.txt",
            # "tmpaccount/2024-09-03-**********.txt",
            "tmpaccount/2024-09-06-**********.txt",
            "tmpaccount/2024-11-07-**********.txt",
        ]
        oldList = {}
        newList = {}
        from Model.Castle import Castle

        with open(paths[0], "r") as f:
            for line in f.readlines():
                if line.strip():
                    castle = Castle(line.strip())
                    if castle:
                        oldList[castle.id] = castle
                    else:
                        print(f"异常数据:{line}")

        with open(paths[1], "r") as f:
            for line in f.readlines():
                if line.strip():
                    castle = Castle(line.strip())
                    if castle:
                        newList[castle.id] = castle
                    else:
                        print(f"异常数据:{line}")

        newComein = []
        nameChange = []
        locChange = []
        lost = []
        for id in newList:
            item = newList[id]
            if id in oldList:
                oldItem = oldList[id]
                if item.name != oldItem.name:
                    nameChange.append(f"{id} {item.loc} {oldItem.name} -> {item.name}")
                if item.loc != oldItem.loc:
                    locChange.append(f"{id} {item.name} {oldItem.loc} -> {item.loc}")
                del oldList[id]
            else:
                newComein.append(
                    f"{id} [{item.allianceName}] {item.name} {item.level} {item.loc}"
                )

        for id in oldList:
            item = oldList[id]
            lost.append(f"{id} [{item.allianceName}] {item.name} {item.level}")

        lostStr = "\n".join(lost)
        nameStr = "\n".join(nameChange)
        locStr = "\n".join(locChange)
        newStr = "\n".join(newComein)
        print(f"离开玩家:{len(lost)}\n{lostStr}")
        print("------------------")
        print(f"改名玩家:\n{nameStr}")
        print("------------------")
        print(f"迁移玩家:\n{locStr}")
        print("------------------")
        print(f"新来玩家:{len(newComein)}\n{newStr}")

    def test_tryShrineSkill(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LVCAr2o-Ngt7QdGPreBKxRBrdj9OM34HQ-v7WNg7mks"
        )
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        print(u1.tryShrineSkill(101))

    def test_cvcRank(self):
        u1 = self.loadGuestAccount()[0]
        if u1.login():
            res = u1.cvcRankContinent(eventId="67a831819d8d827cc28d2190")
            if res:
                ranking = res.get("ranking")
                for rank in ranking:
                    point = rank.get("point")
                    kingdom = rank.get("kingdom")
                    name = kingdom.get("name")
                    alliance = kingdom.get("alliance", {})
                    tag = alliance.get("tag")
                    print(f"{name}[{tag}] {point}")
                return

        #     while True:
        #         u1.tryCvcRankMonitor()
        #         u1.randomSleep(60, msg="1分钟测试")
        from Celery.scheduleTask import autoCvcRankMonitor

        while True:
            autoCvcRankMonitor(
                userKey="b220f0e4-8a3b-2de3-015d-70c0d2b7a3fa", sendNoti=False
            )
            time.sleep(60)

        # cvcRankContinent

    def test_mint2(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.dfx1cK4v2B4DRnQNS56xgqP_9cqWYXP5xqzW0OqkB-g"
        )
        u1.loadEmailWithToken()
        print(u1.claimDragoMint(dragoId="62a18cb4dac142b8bda0e1b7"))

    def test_bug(self):
        #     # u1 = UserInfo(userKey="9241e5bd-9c62-a548-74f0-abad1b695d83")
        u1 = UserInfo(
            "<EMAIL>",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.q3EYIIGkdK6RRmeUIAA0rj75-BXUSLdc7fpoPtvp_Mw",
        )
        u1.isGoogle = True
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        # u1.wsWithKingdomApp(isDebug=True)
        u1.resources = [*********, *********, *********, *********]
        print(u1.listDragoMint())
        u1.deleteAccount()
        # u1.cancelDragoMint("62a18cb4dac142b8bda0e1b7")
        # if u1.tryLinkWallet("0x1dff245dfce3647819d004ddd6dd936ad1a959fa086344840dbb9e3d426f2897"):
        #     u1.dragoLairJoin('62a18cb4dac142b8bda0e1b7')
        #     u1.tryUseDragonMint()

    #     # print(u1.build(104,********,instant=1))
    # print(u1.tryUseDragonMint())
    #     # print(u1.dragoLairList())
    #     # u1.dragoLairJoin('62a18cb4dac142b8bda0e1b7')
    #     # print(u1.tryUseDragonMint('62a18cb4dac142b8bda0e1b7'))
    #     # if u1.login():
    #     #     u1.wsWithKingdomApp(isDebug=True)
    #     #     u1.randomSleep(5,msg="等待ws连接")
    #         # print(u1.dsavipshopList())
    #         # print(u1.dsavipshopBuy())
    #     # u1.runTasks([u1.bugTrainTroopA(********),u1.bugTrainTroopA(********,delay=3),u1.bugTrainTroopA(********,delay=6)])

    #         # u1.randomSleep(20)

    def test_rebuild(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iZ8ySsKc_DFQjVv_S9CjTgLcEAn7tPnlJlK7C6_Eakg"
        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        buildPosition = 118
        buildCode = 40100205
        print(u1.build(buildPosition, buildCode, False, 1))
        time.sleep(5)
        print(u1.build(buildPosition, buildCode, False, 1))

    def test_autoTreatment(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.LAqE1hPPC42NiVth5aSgHGZIHRIZUy-_StfaryCUgZA"
        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.debugToConsole()
        print(u1.autoTreatment())
        # if u1.enter():
        #     u1.checkResearchLevel()
        #     print(f'研究等级:{u1.researchGrade}')

    def test_tryGarrison(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.d5mZ_rLjH6cI5kylvKEMZUJpGNWqP6PW2wTx323LXIA"
        u1 = self.buildTokenAccount(token, socks5="127.0.0.1:1087")
        u1.fieldObjectId = "668b7bb99db7cc5bca0e4f57"
        u1.realWorld = 20

        assert u1.tryGarrison([100002, 1800, 800])

    def test_mint(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************.DIHHFUFndqRdLaXomCqut52Wn7GMz0fbv3Hv7pqC3BE"
        u1 = self.buildTokenAccount(token, socks5=random.choice(loadS5List()))
        u1.isWebDevice = True
        if u1.enter():
            u1.tryDragoMint()

    def test_drago_leave(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************.QFe2L-wOfOYFfpncAvqaVTCkhAWLIVn7DgEfqSmEiVs"
        u1 = UserInfo("test", token=token, socks5=random.choice(loadS5List()))
        u1.isWebDevice = True
        u1.initLog(False)

        dragos = u1.nftDragoList()
        for drago in dragos:
            tokenId = drago.get("tokenId")
            if tokenId in [11546, 20090]:
                print(u1.dragoLairLeave(drago.get("_id")))
                # print(u1.dragoLairLeave(drago.get("_id")))

    def test_up_skill(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWMxNGRjNzc1ZmNiNDEwNWE4NjlkZjYiLCJraW5nZG9tSWQiOiI2MWMxNGRjNzc1ZmNiNDEwNWE4NjlkZjciLCJ3b3JsZElkIjoyMCwidmVyc2lvbiI6MTU0OCwiYnVpbGQiOjAsInBsYXRmb3JtIjoid2ViIiwidGltZSI6MTY3MDQ2MjQ3NDU4OSwiY2xpZW50WG9yIjoiMCIsImlhdCI6MTY3MDQ2MjQ3NCwiZXhwIjoxNjcxMDY3Mjc0LCJpc3MiOiJub2RnYW1lcy5jb20iLCJzdWIiOiJ1c2VySW5mbyJ9.3m52D_F-ehznHY4pZdaWxq0flmWscF4Nt4ipYSmZxAw"
        _s5List = loadS5List()
        localS5 = None
        # localS5 = "127.0.0.1:1086"

        u1 = UserInfo("somebody", token=token, socks5=localS5)
        u1.isWebDevice = True
        u1.initLog(False)
        # if u1.login():
        #     u1.wsWithKingdomApp()

        u1.tryUseUpSkillToUse("61e1459021cced0faac9b302")

    def test_allianceSearch(self):
        token = """eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.U7B-qoOa7xCprSe19US9cNDzJ_A-dKkjxx6Svk8anv8"""
        user = self.buildTokenAccount(token)
        user.allianceId = "63610429b864dc259f4a635c"
        print(user.tryJoinAllianceByTag("LDAT"))
        # # print(json.dumps(user.allianceSearch(keyword="ATAT"),ensure_ascii=False))
        # print(json.dumps(user.allianceHelpList(),ensure_ascii=False))

    def test_spin(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.newRDlTm06uaIcd5HAWVhAmnDQ8yml6IsdwMdMUAtiQ"
        u1 = self.buildTokenAccount(token, socks5="127.0.0.1:1087")
        print(u1.treasureHuntSpinAll(********))

    def test_tryUseRedDragon(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hgB1ns5M1sVVemKGK1dn_pP7avX_PkkXP2MIOjy5QWw"
        localS5 = "127.0.0.1:1086"
        u1 = UserInfo(token=token, socks5=localS5)
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.itemList()
        u1.tryUseRedDragon()

    def test_build_hospital(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************.RTFi6WKMkkuMIANTyPWvnVZPtYku5OrTvCNURuisxOU"
        s5List = loadS5List()
        localS5 = None
        import platform

        if platform.processor() != "arm":
            localS5 = "127.0.0.1:1087"
        else:
            localS5 = random.choice(s5List)

        u1 = UserInfo("somebody", token=token, socks5=localS5)
        u1.loadEmailWithToken()
        u1.isWebDevice = True
        # if u1.login():
        #     u1.wsWithKingdomApp()

        u1.build(116, 40100106)

    def test_auto_upgrade(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.idtdNeLceYS5Z8BNsII7aMeNQYiBFhXfYUSi_pbSXk8"
        s5List = loadS5List()
        localS5 = None
        import platform

        if platform.processor() != "arm":
            localS5 = "127.0.0.1:1087"
        else:
            localS5 = random.choice(s5List)

        u1 = UserInfo("somebody", token=token, socks5=localS5)
        u1.loadEmailWithToken()
        u1.isWebDevice = True
        if u1.enter():
            # u1.wsWithKingdomApp()
            # u1.tryUseCollectionSkill()
            MaxLevel = 35
            for currentMaxLevel in range(15, MaxLevel + 1):
                for build in u1.buildings:
                    position = build["position"]
                    level = build["level"]
                    if position > 10 and level < MaxLevel:
                        while level < 30:
                            u1.randomSleep(3, 5)
                            u1.buildingUpgrade(position, level, instant=1)
                            level += 1
                            u1.log(f"砖石秒{position}:{level}")
                        # continue
                        while level < currentMaxLevel:
                            u1.randomSleep(
                                1,
                                2,
                                msg=f"准备升级:{position} {level}/{currentMaxLevel}",
                            )
                            res = u1.buildingUpgrade(position, level)
                            if res:
                                newTask = res.get("newTask")
                                leftTime = u1.trySpeedUpUseItem(
                                    1, kingdomTasks=[newTask]
                                )
                                if leftTime == 0 or leftTime is True:
                                    level += 1
                                else:
                                    if leftTime > 0 and leftTime < 60:
                                        level += 1
                                        u1.speedup(newTask.get("_id"), 10103001, 1)
                                    else:
                                        u1.log(f"leftTime{leftTime}")
                                        raise UserError.finish()
                            else:
                                u1.log("升级异常")
                                raise UserError.finish()
                    build["level"] = level

    def test_lstProtect(self):
        from Api.User.Request import (
            UserRequest,
            b64ApiList,
            checkB64ApiList,
            domain,
            returnB64ApiList,
        )

        print(returnB64ApiList())
        checkB64ApiList()
        print(returnB64ApiList())

    def test_get_token(self):
        print("\n")
        print(requestToken())

    def test_googleCaptcha(self):
        from Api.GoogleCaptcha import GoogleCaptcha

        g = GoogleCaptcha([], 5)
        while True:
            time.sleep(3)
            print("拿码")
            g.getOneToken()

    def test_checkBalance(self):
        from Api.GoogleCaptcha import getBalance
        from Api.IPIdeaApi import myBalance
        from Api.ProxyApi import changeKeyTo1, changeKeyTo2, quota
        from Api.TTApi import checkPoints

        # changeKeyTo1()
        googleBalance = getBalance()
        lzApi = checkPoints().get("balance")
        # lzApi = checkPoints().get('availablePoints')
        proxyApi = quota().get("Available")
        IPIdeaApi = myBalance().get("balance")
        logger.info(
            f"\ngoogle:{googleBalance}\nlzApi:{lzApi}\nproxy:{proxyApi}\nIPIdea{IPIdeaApi}"
        )

    def test_refreshMetaData(self):
        from Unit.MarketUnit import requestRefreshMetaData

        asyncio.run(requestRefreshMetaData(**********))

    def test_register_gust(self):
        from guestRegister import registerWithDynamicProxy

        registerWithDynamicProxy(isV2=True, maxCount=1)

    def test_get_google_account(self):
        # Install the Python Requests library:
        # `pip install requests`

        import requests

        def send_request():
            # 获取账号
            # POST http://dxh001.f3322.net:2222/system/deposit/api/getaccount

            try:
                response = requests.post(
                    url="http://dxh001.f3322.net:2222/system/deposit/api/getaccount",
                    headers={
                        "Content-Type": "application/x-www-form-urlencoded; charset=utf-8",
                    },
                    data={
                        "username": "3241001",
                        "pwd": "123456",
                    },
                )
                res = response.json()
                data = res.get("data")
                status = data.get("status")
                if status == "1":
                    logger.info(
                        f"{data.get('account')}----{data.get('password')}----{data.get('token')}"
                    )
                else:
                    logger.info(f"{data}")
            except requests.exceptions.RequestException:
                print("HTTP Request failed")

        for _i in range(2):
            send_request()

    def test_socks(self):
        s5List = loadS5List()
        for s5 in s5List:
            try:
                logger.info(getIP(s5))
            except Exception as e:
                logger.error(f"socks5: {s5},{e}")

    def test_proxyApi(self):
        from Api.ProxyApi import allocateIP, changeKeyTo1, createRealSocks5

        changeKeyTo1()
        ip = allocateIP()
        print(ip)
        print(getIP(createRealSocks5(ip)))

    def test_IPIdeaSocks(self):
        # from Api.IPIdeaApi import allocateIP
        from Api.ProxyApi import (
            allocateIP,
            changeKeyTo3,
            changeKeyTo4,
            createRealSocks5,
        )

        changeKeyTo4()
        s5 = allocateIP()
        if s5:
            logger.info({"socks": s5})
            logger.info(createRealSocks5(s5))
            # logger.info(getIP(s5))
        else:
            logger.info("获取失败")

    def test_async_get_ip(self):
        from Api.Ipinfo import asyncGetIP

        print("测试异步获取IP")
        ip = asyncio.run(asyncGetIP("127.0.0.1:1086"))
        self.assertEqual(ip, "**************")
        logger.info(ip)

    def test_async_allocate_ip(self):
        from Api.ProxyApi import asyncAllocateIP

        host = asyncio.run(asyncAllocateIP())
        self.assertNotEqual(host, None)
        logger.info(host)

    def test_wsgetField(self):
        _s5List = loadS5List()
        from Api.FlaskHelper import requestToken, requestTokenA
        from Unit.UserInfoHelper import searchAll

        # users = self.loadGuestAccount()
        # [v.login() for v in users]
        # token = requestToken()
        # token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vAN6_a5heq4XwMzDPby3efRA__XDkfbe3tyxyZXjgGc"
        # socks5 = "fjxnuduv:gvwiwc08ayac@**************:6350"
        # socks5 = "127.0.0.1:1087"
        _socks5 = None
        # user = UserInfo(f'king_{random.randint(1, 9999)}@search.com',
        # token=token, socks5=socks5)  # random.choice(s5List))
        user = UserInfo(userKey="029a0457-8417-c379-0d2a-faa44b2835d7")
        user.loc = [20, 1024, 1024]
        user.debugToConsole()
        user.token = user.loadRedisToken()
        # user.initLog(needTestS5=False)
        if True or user.login():
            t1 = time.time()
            for _ in range(1):
                objects = user.wsGetFields([20, 1387, 395], power=3, more=True)
            t2 = time.time()
            obs = [item for subList in objects.values() for item in subList]
            t3 = time.time()
            obsReSort1 = user.returnSortFields(obs)
            obsReSort2 = user.returnSortFields(obs, levelPriority=True)
            self.assertNotEqual(obsReSort1, obsReSort2)
            t4 = time.time()
            logger.info(
                f"总耗时:{round(t4 - t1,2)} 搜索耗时:{round(t2 - t1,2)} 排序耗时:{round(t4 - t3,2)}"
            )
            return

        def marchCallBack(objects):
            logger.info(f"objects:{objects}")
            map(lambda v: (v.get("_id")), objects)

        # logger.info(user.coordinateTransformation(user.loc,0))
        # return
        for i in range(500):
            if i % 20 == 0:
                user.taskAll()
            # user.loc = [random.randint(1, 60), random.randint(
            #     100, 2000), random.randint(1000, 2000)]
            # b = user.searchCrystalWithZoneSize(user.loc)
            t1 = time.time()
            res = user.getInfoAndCheck([20, 1024, 1024])
            # b = user.wsGetFields(zone=[20,2080],marchCallBack=marchCallBack,waitMarch=True,isDebug=True)
            # [logger.info(f'{v}:{len(b[v])}') for v in b]
            t2 = time.time()
            logger.info(f"耗时:{round(t2 - t1,2)},{res and '成功' or '失败'}")
            # print(user.wsGetFields(power=4,isDebug=True))#,zone=[0,64,1,65]))
            # break
            time.sleep(1)

    def test_wsgetFieldReal(self):
        # users = self.loadGuestAccount()
        userKey = "5b8d11e9-db40-197f-534a-3714e6887b0a"
        socks5 = "127.0.0.1:1087"
        user = UserInfo(userKey=userKey, socks5=socks5)
        loc = [100003, 1500, 1500]
        # user.debugToConsole()
        user.initLog(needTestS5=False)
        user.token = user.loadRedisToken()
        if user.token or user.login():
            t1 = time.time()
            data = user.wsGetFields(
                loc=loc,
                power=2,
                isDebug=False,
                more=True,
                isCVC=True,
                zone=user.coordinateTransformation(loc, power=1),
            )
            t2 = time.time()
            print(f"耗时:{round(t2 - t1,2)}")
            for v in data:
                if v in enum.OBJECT_MONSTER_CODE_LIST:
                    d = data[v]
                    print(f"{fieldNames[v]} {len(d)}")
            # 打印data中的所有数据
            for v in data:
                if v in enum.OBJECT_MONSTER_CODE_LIST:
                    d = data[v]
                    print(f"{fieldNames[v]} {len(d)}")
                    [print(k) for k in d]

            # print(f"数据类型:{[fieldNames[v] for v in data.keys()]}")

    def test_itemList(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.iR7H6p103WJfoDFZJUyjCGfwwlbpBXQPxNe_iOun1Ww"
        )
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)

        u1.itemList()

    def test_claimEvent(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4QX0_oJg8Tm9mHZDykAck7NJ6HbNoJdZHX9oKhUJ4xY",
            socks5="jj:jj@************:20324",
        )
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.debugToConsole()
        u1.claimCVCEvent()

    def test_collectionEvent(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.vf38q-YMtay-5X0n_8vyaWfJEwAMCw951SIKHDJ-sXQ"
        socks5 = "127.0.0.1:1087"
        u1 = UserInfo(token=token, socks5=socks5)
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.debugToConsole()

        u1.eventList()

        # print(json.dumps(u1.pkgList(pkgType=8), ensure_ascii=False))
        # print(json.dumps(u1.eventInfo("674281c551ec20913a3ca283"),ensure_ascii=False))
        # print(json.dumps(u1.gatheringDashBoard(),ensure_ascii=False))
        # u1.autoCollectionEvent()
        # print(json.dumps(u1.anyOpen(),ensure_ascii=False))
        print(json.dumps(u1.cvcEventList(), ensure_ascii=False))

    def test_startRally(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._HKpBXEb140JRrXOo3XA6_xIU-wMLE3aLeb0UDU7Ifk"
        )
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        tasks = None
        u1.loc = [20, 916, 1252]
        u1.marchSize = 300000
        u1.marchLimit = 10
        # print(json.dumps(u1.taskAll()))
        if u1.startRally([20, 1108, 1030], 3, 100):
            while True:
                tasks2 = u1.taskAll()
                if tasks2 != tasks:
                    print(json.dumps(tasks2))
                    tasks = tasks2
                time.sleep(5)

    def test_task(self):
        from Celery.scheduleTask import listDragoMinesTask

        listDragoMinesTask()

    def test_decrpt(self):
        u1 = UserInfo()
        d = u1.decrypt(
            "VUxLVUIUCUVPAxVcWwsVEl8EEmUMSgoMFBUbb1gFRwtPD1QaGghdAQcCVlRcXAUfG14MVABQBlRZG00MTQFdVRNbXR1PShVPWhtKEgtQSRMeTQBcWgtdEgtDVwFfCkwfH0MJA2VQUQtdCVsbGkAPAgE7Rx1PXBleSw1NVVUkC1UIXUMUDFwJAgJMVABACVR6H10DAgVbUQlDC1YddEwVEkUICFRPA1AZHlgKAx9XUAJVDVcfG10BBB1DFVAfWAwMFBUbXFQXAF1PA1IcAkxJX0IIEVgCV0MUH18BHBMDEFgBXQhASS1WVFRDXwVdCFEeHF4ITUxNR0MISg5bXA1cQxNbPgBdDlUaGV0PBwhVSQBfCFcWFlwICQBXSwhUAFgXFkIACABQUAJfD1QYAFsVAQNZUgVUDlgbF0AABAhYXAhVZE0MWx5dUUUEJ0QEVQVHQAkbCkpDFV4eUBVHQQAbCgBQXR1PWg5KS0wDBAFQVQFfCVACDB1NUUUERwtfFUNCSxhcXBNbVgBBGwNbRwJdVVVDXxNfCVMdA18IHQFSMQBZA1EeFFoPHgVQU2tPFUNCTx1NZUEGF1AJXAUMFEwLAANSSABcFFEdel8NCgFRXwVbF1UfGDQbTUw="
        )
        print(d)

    def test_drago_mines(self):
        asyncio.get_event_loop()
        os.environ["LEAGUEDEBUG"] = "1"
        token = None  # "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.NcPcCJtnEwtK4VYOV0-4yu3k9oAswwhL_PmnPR0OxgQ"
        socks5 = None  # "127.0.0.1:7890"
        userKey = "56172c7d-3c90-c4bb-e6fe-eb3463d14255"
        user = UserInfo(
            f"king_{random.randint(1, 9999)}@search.com",
            userKey=userKey,
            token=token,
            socks5=socks5,
        )
        if user.userKey:
            user.login()
        else:
            user.loadEmailWithToken()
        user.loc = [20, 1024, 1024]

        def fieldCallBack(object):
            # for object in objects:
            try:
                loc = object.get("loc")
                occupied = object.get("occupied")
                level = object["level"]
                code = object["code"]
                if code == Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN:
                    print(f'龙矿 {loc} {level} {occupied and "占领" or "未占领"}')
            except Exception as e:
                print(e)

        while True:
            t1 = time.time()
            zones = []
            datas = user.searchCrystalWithZoneSize(
                user.loc, zones=zones, callBack=fieldCallBack
            )
            logger.info(len(datas.get(Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN)))
            user.randomSleep(60, msg=f"耗时 {round(time.time()- t1,2)}")
            break

    def test_global_crystal(self):
        print("")
        s5List = loadS5List()
        _searchFields = [20100105]  # [20100105,20200104]

        crystalList = {}
        crystalHiddenList = {}
        occupiedCrystalHiddenList = {}
        userList = {}
        diffList = []
        highLevelList = []
        from Api.FlaskHelper import requestToken, requestTokenA

        t1 = time.time()
        # tasks = UserInfo().runTasks([requestTokenA() for v in range(4)])
        # tokens = [task.result() for task in tasks[0]]
        tokens = [
            "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.__MU4dH_PzvRzvLYYWHs5MDh2ekmw925xBsG-cdpg6g"
            for i in range(4)
        ]
        userKeys = [
            "473c138a-effd-3ba7-c220-bdd5a9ce3715",
            "2de1853c-93ba-471d-2d0a-bc387dde096c",
            "7502caaf-a521-ea95-cfd3-8f928d9db416",
            "91cb01ad-0036-2d69-3a8c-e2dd49fb8c13",
            "bde52b4a-f2d3-0d23-4586-6a58c036e875",
            "ec6cdf2a-ce5b-b20d-05d7-2b70fa96f83e",
            "7025d268-161c-d896-c194-e8010d564e16",
            "38709536-4002-3478-a342-c50eb318fd2b",
        ]
        users = [
            UserInfo(userKey=userKey, socks5=random.choice(s5List))
            for userKey in userKeys
        ]
        [user.login() for user in users]
        tokens = [user.token for user in users]
        from Unit.UserInfoHelper import searchAll

        for _i in range(500):
            worldId = random.randint(1, 60)
            data = searchAll(tokens, [worldId, 1024, 1024])
            print(len(data[20100105]))
            time.sleep(5)
        return
        # tokens = ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjUiLCJraW5nZG9tSWQiOiI2MmI0ZmZmMWUxYmEwMTZmMWJjMjA1ZjYiLCJ3b3JsZElkIjo0MiwidmVyc2lvbiI6MTQ2NiwiYnVpbGQiOjAsInBsYXRmb3JtIjoiaW9zIiwidGltZSI6MTY1ODc0MTA4NzM4NCwiY2xpZW50WG9yIjoiMCIsImlhdCI6MTY1ODc0MTA4NywiZXhwIjoxNjU5MzQ1ODg3LCJpc3MiOiJub2RnYW1lcy5jb20iLCJzdWIiOiJ1c2VySW5mbyJ9.0wUQriKyFVzecZQuKIucKK2HpQhFJE4EoE60JLqDNBc" for v in range(4)]
        t2 = time.time()
        # token = requestToken()
        if not tokens:
            print("获取token失败")
            exit(0)
        users = [
            UserInfo(
                f"king_{random.randint(1, 9999)}",
                token=token,
                socks5=random.choice(s5List),
            )
            for token in tokens
        ]
        # u1.log(u1.taskAll())
        # u1.wsField = "https://socf-lok-live.leagueofkingdoms.com/socket.io/"

        def fieldCallBack(object):
            # for object in objects:
            try:
                loc = object.get("loc")
                occupied = object.get("occupied")
                level = object["level"]
                if not occupied:
                    state = object.get("state")
                    if state == 1:
                        # value = object["param"]["value"]
                        # print(f'无人的{level}级水晶矿{loc} {value}')
                        hidden = object.get("hidden")
                        if not hidden:
                            crystalList[f"{loc[1]}_{loc[2]}"] = level
                        else:
                            crystalHiddenList[f"{loc[1]}_{loc[2]}"] = level
                else:
                    allianceTag = occupied.get("allianceTag")
                    allianceTags = ["ATAT", "LDAT"]
                    name = occupied.get("name")
                    occupiedCrystalHiddenList[f"{loc[1]}_{loc[2]}"] = object
                    if userList.get(name):
                        userList[name] += 1
                    else:
                        userList[name] = 1
                    locStr = ",".join([str(v) for v in loc])
                    id = ""  # occupied.get("id")
                    logStr = f"{level}\t{locStr}\t{allianceTag}\t{name}\t{id}"

                    if level >= 3:
                        highLevelList.append(logStr)
                    if allianceTag not in allianceTags:
                        diffList.append(logStr)
                    # print(f'有人的{object.get("loc")}')
                # print(object)
            except Exception as e:
                print(e)

        loc = [20, 1024, 1024]
        # users[0].loc = [21,911,1153]
        # users[0].searchCrystalWithZoneSize([21,911,1153],zoneSize=147,callBack=fieldCallBack)
        zones = crystalLands
        for index in range(len(users)):
            user = users[index]
            user.loc = loc
            _b = user.searchCrystalWithZoneSize(
                loc,
                zones=zones[
                    index * MaxSixThreadZoneSize : (index + 1) * MaxSixThreadZoneSize
                ],
                callBack=fieldCallBack,
            )
            # print(b)
        crystalStrList = []

        for v in crystalList:
            crystalStrList.append(f"{v} {crystalList[v]}级")
        logger.info(f"无人水晶矿 {len(crystalList)}个\n" + "\n".join(crystalStrList))
        t3 = time.time()

        hiddenStr = f"地下矿{len(crystalHiddenList)}:\n"
        if len(crystalHiddenList):
            hiddenStrList = []
            for v in crystalHiddenList:
                hiddenStrList.append(f"{v} {crystalHiddenList[v]}级")
            hiddenStr = hiddenStr + "\n".join(hiddenStrList)
        logger.info(hiddenStr)

        if len(diffList):
            diffStr = "\n".join(diffList)
            logger.info(f"异盟水晶({len(diffList)}):\n{diffStr}")

        if len(highLevelList):
            highLevelStr = "\n".join(highLevelList)
            logger.info(f"高级水晶({len(highLevelList)}):\n{highLevelStr}")

        userSortKeys = sorted(userList.items(), key=lambda x: x[1], reverse=True)
        ranks = []
        for key in userSortKeys:
            if key[1] > 2:
                ranks.append(f"{key[0]}:{key[1]}")
        rankStr = "\n".join(ranks)
        crystalSum = (
            len(crystalList) + len(crystalHiddenList) + len(occupiedCrystalHiddenList)
        )
        logger.info(f"排行榜({crystalSum}):\n{rankStr}")
        logger.info(
            f"获取水晶矿耗时{t3-t2} 秒 获取token耗时{t2-t1} 秒 总耗时{t3-t1} 秒"
        )
        # maxLen = len(zones) // 49 + 1
        # for i in range(maxLen):
        #     z = [21] + zones[i*49:i*49+49]
        #     o = u1.wsGetFields([21,1024,1024],zone=z)
        #     if o and o.get(20100105):
        #         fieldCallBack(o[20100105])
        # fieldCallBack(o)
        # random.shuffle(zones)
        # u1.wsGetFields([21,1024,1024],zone=zones)
        # u1.searchCrystalAutomaticCutting(21, zones,fieldCallBack=fieldCallBack,isDebug=True,roundCallback=exitCallBack,fast=True)

        # while True:
        #     time.sleep(1)

    def test_serarch(self):
        print("")
        t1 = time.time()
        s5List = loadS5List()
        searchFields = [20300101]  # [20100105,20200104]
        from Api.FlaskHelper import requestToken, requestTokenA

        tasks = UserInfo().runTasks([requestTokenA() for v in range(2)])
        tokens = [task.result() for task in tasks[0]]

        if not tokens:
            print("获取token失败")
            exit(0)
        t2 = time.time()
        loc = [47, 1024, 1024]
        users = [
            UserInfo(
                f"king_{random.randint(1, 9999)}",
                token=token,
                socks5=random.choice(s5List),
            )
            for token in tokens
        ]
        # user.loc = loc
        # b = user.searchFieldWithZoneSize(loc,zoneSize=49,codes=searchFields)
        # print(b)
        zones = [v for v in range(4096)]
        avgSize = len(zones) // len(users)
        allResults = []
        pool = ThreadPoolExecutor(
            max_workers=len(users), thread_name_prefix="test_search_"
        )

        def threadRun(user, zones):
            results = {}
            try:
                results = user.searchFieldWithZoneSize(
                    user.loc, zones=zones, codes=searchFields
                )
            except Exception as e:
                print(f"some error {e}")
            # finally:
            #     print("over?")
            return results

        allTasks = []
        for index in range(len(users)):
            user = users[index]
            user.loc = loc
            allTasks.append(
                pool.submit(
                    threadRun, user, zones[index * avgSize : (index + 1) * avgSize]
                )
            )

        pool.shutdown(wait=True)

        for future in allTasks:
            objects = future.result()
            allResults += objects.get(20300101, [])

        t3 = time.time()
        print("over")
        allValues = {}
        for result in allResults:
            occupied = result.get("occupied")
            name = occupied.get("name")
            id = occupied.get("id")
            loc = ",".join([str(v) for v in result.get("loc")])
            level = result.get("level")
            allianceTag = occupied.get("allianceTag", "")
            allValues[name] = f"{id}\t{name}\t{level}\t{loc}\t{allianceTag}"
            if allianceTag == "NDAT":
                print(f"{name}:{allValues[name]}")
        with open("users.txt", "w") as f:
            # pass
            f.write("\n".join(allValues.values()))

        t4 = time.time()
        print(
            f"处理数据耗时{t4-t3}秒 获取数据耗时{t3-t2} 秒 获取token耗时{t2-t1} 秒 总耗时{t4-t1} 秒"
        )

    def test_global_search(self):
        print("")
        s5List = loadS5List()
        searchFields = [20100105]  # [20100105,20200104]

        def searchForLoc(loc):
            token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MjU4YjhiOGMwOWU4YjZlMzA5NWMyZDgiLCJraW5nZG9tSWQiOiI2MjU4YjhiOGMwOWU4YjZlMzA5NWMyZDkiLCJ2ZXJzaW9uIjoxNDI2LCJ0aW1lIjoxNjUzMDMyMzg4NjM2LCJpYXQiOjE2NTMwMzIzODgsImV4cCI6MTY1MzYzNzE4OCwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.3zH7TZL3Z5_D0ht94deWwjFncf5ER_sUFXvKuRqAiKo"
            u1 = UserInfo("king", token=token, socks5=random.choice(s5List))
            u1.wsField = "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
            resList, resObjects = u1.searchFields(
                searchFields, loc, power=3, noNull=False, noHuman=True
            )

        # 5*32 = 160
        threads = []
        stage = 12
        startPoint = (2048 - stage * 160) / 2
        # if startPoint < 0:
        #     logger.error("阶太大了")
        #     return
        for i in range(stage * stage):
            x = i % stage
            y = int(i / stage)
            loc = [21, startPoint + x * 160, startPoint + y * 160]
            th = threading.Thread(target=searchForLoc, args=(loc,))
            th.setDaemon(True)
            th.start()
            threads.append(th)

        for th in threads:
            th.join()
        from Api.User.WebSocket import localMap

        for key in localMap:
            if localMap[key] > 2:
                logger.info(f"{key}:{localMap[key]}")

    def test_global_search_man(self):
        print("")
        s5List = loadS5List()
        # searchFields = [20100101,20100102,20100103,20100104,20100105,20300101]#[20100105,20200104]
        searchFields = [20300101]
        descIds = [
            # "61ad6b118d179c3fd5d50f09",
            # "6209b4a1aca7c54990731dd8",
            # "61e7df4da5f65c3215bf7492", #UurJr
            # "61b1780fcbeea01106fcadfb",
            # "61b9ddc4b59da31908accb3c",
            # "61bab610efd5980cde81d5fd",
            # "61985fda7c44012aef562b93",
            # # "61c17270106506203538dd48",
            # "61b9847c120364170979b239",
            # "61d0342a28fb641455c058bd",
            # "61adc063157a3732b372eaf8",
            # "61aea6e5a19a261d5674eaef",#"离别无心",
            # "61bcae6685bd77119946e74f",
            # "61a7b38519f8a40ca6e09851", #yuyuasi
            # "61c0ed1e9b34ef20470de261",
            # "61c0ed1e9b34ef20470de263",
        ]
        allianceIds = [
            # "61adc3925d9e240c708b5704" #mgg4
            # "61ae2df2298f741222be23d2",
            # "61ce1d50c82b2c1097d79764"
            "61af2691afd6910ca9549cb3",
            "61f961e880aaf7112ecedf64",
        ]
        names = [
            # "UurJr",
            # "MGG-98k",
            # "MGG-TAZADAR",
            # "MGG-Luo",
            # "MGG-Baazil",
            # "WAXP BSW",
            # "worbecar"
        ]

        def searchForLoc(loc):
            u1 = UserInfo("king", socks5=random.choice(s5List))
            u1.wsField = "https://socf-lok-live.leagueofkingdoms.com/socket.io/"
            # u1.searchFields(searchFields,loc,power=3,noNull=False,noHuman=False)
            resList, resObjects = u1.searchFields(
                searchFields, loc=loc, power=3, show=False, noHuman=False
            )
            if resObjects is None:
                u1.log(f"{loc} 请求异常")
                return
            # u1.log(f"数据{len(resObjects)}")
            for info in resObjects:
                occupied = info.get("occupied")

                find = False
                if occupied:
                    id = occupied.get("id")
                    allianceId = occupied.get("allianceId")
                    name = occupied.get("name")
                    shield = occupied.get("shield")
                    code = info.get("code")
                    # if shield != 0:
                    #     continue
                    if id in descIds:
                        find = True
                        # u1.log(f'{id}')
                    elif allianceId in allianceIds:
                        find = True
                    else:
                        if name in names:
                            find = True
                            u1.log(f"{id}")
                        # elif name[:3] == "MGG":
                        #     loc = info.get("loc")
                        #     if abs(896-loc[1]) < 100 and abs(1152-loc[2]) < 100:
                        #         find = True
                        # else:
                        #     u1.log(f"{name}|{id}|{info.get('loc')}")
                    if find:
                        u1.log(
                            f"搜索到了 {name} {info.get('loc')} {fieldNames.get(code)} {shield and '开盾' or '没盾'}"
                        )

        # 5*32 = 160
        threads = []
        stage = 12
        startPoint = (2048 - stage * 160) / 2
        # if startPoint < 0:
        #     logger.error("阶太大了")
        #     return
        for i in range(stage * stage):
            x = i % stage
            y = int(i / stage)
            loc = [21, startPoint + x * 160, startPoint + y * 160]
            th = threading.Thread(target=searchForLoc, args=(loc,))
            th.setDaemon(True)
            th.start()
            threads.append(th)

        for th in threads:
            th.join()

    def test_search_kingdom(self):
        from Api.FlaskHelper import requestTokenA
        from Unit.UserInfoHelper import searchAll

        tasks = UserInfo().runTasks([requestTokenA() for v in range(4)])
        tokens = [task.result() for task in tasks[0]]
        # ,zones=[[v for v in range(2048,3095)]])
        data = searchAll(tokens, [32, 1024, 1024], codes=[Enum.OBJECT_CODE_KINGDOM])
        kingdoms = data.get(Enum.OBJECT_CODE_KINGDOM)
        names = []
        for kingdom in kingdoms:
            if kingdom["level"] > 15:
                names.append(kingdom["name"])

        # highKingdoms = filter(lambda kingdom: kingdom.get("level") > 25 , kingdoms or [])
        # names = map(lambda kingdom: kingdom["name"], [v for v in highKingdoms])
        # names = [v for v in names]
        with open("name.txt", "a+") as f:
            [f.write(f"{name}\n") for name in names]

        logger.info("\n".join(names))

    def test_search_drago_mine_one(self):
        from Api.FlaskHelper import requestTokenA
        from Unit.UserInfoHelper import searchAll

        tasks = UserInfo().runTasks([requestTokenA() for v in range(4)])
        tokens = [task.result() for task in tasks[0]]
        data = searchAll(tokens, [2, 1024, 1024])
        dragoMines = data.get(Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN)
        _mines = filter(
            lambda mine: mine.get("param").get("value") == 1000
            and mine.get("occupied") is None,
            dragoMines or [],
        )

    def test_search_drago_mine(self):
        from Api.FlaskHelper import requestTokenA
        from Unit.UserInfoHelper import searchAll

        tasks = UserInfo().runTasks([requestTokenA() for v in range(4)])
        tokens = [task.result() for task in tasks[0]]
        if tokens:
            outString = ""
            for i in range(60):
                data = searchAll(tokens, [i + 1, 1024, 1024])
                dragoMines = data.get(Enum.OBJECT_CODE_DRAGON_SOUL_CAVERN)
                levels = {}
                for mine in dragoMines:
                    level = mine.get("level")
                    key = f"Lv{level}"
                    if levels.get(key):
                        levels[key] += 1
                    else:
                        levels[key] = 1

                logs = [f"{k}:{levels[k]}" for k in levels]
                log = "\n".join(logs)
                log = f"{i+1}区:\n{log}\n"
                print(log)
                outString += log
                time.sleep(5)
            print(outString)

    def test_UserKey(self):
        s5List = loadS5List()
        with open("./guests.txt") as f:
            lines = f.readlines()
            for line in lines:
                u1 = UserInfo(userKey=line.strip(), socks5=random.choice(s5List))
                try:
                    if u1.login():
                        if u1.enter():
                            delFlag = False
                            if u1.loc[0] == 23:
                                if u1.level >= 5:
                                    u1.deleteAccount()
                                else:
                                    if delFlag:
                                        u1.deleteAccount()
                                    else:
                                        u1.tryLeaveAlliance()
                                        u1.worldChange(21)
                except Exception as e:
                    logger.info(f"异常跳过 {e}")

    def test_download(self):
        import os

        import requests

        os.makedirs("temple", exist_ok=True)
        temples = {
            "A": range(1, 5),
            "B": range(1, 13),
            "C": range(1, 21),
            "D": range(1, 29),
        }

        api = "https://api-lok-beta.leagueofkingdoms.com/api/land/zone/lands"

        def downloadWithTemple(zone, index):
            res = requests.get(api, params={"zoneId": f"{zone}{index}"})
            if res.status_code == 200:
                resJson = res.json()
                if resJson["result"]:
                    with open(f"temple/{zone}{index}.json", "w") as f:
                        f.write(json.dumps(resJson["lands"]))

        def checkJsonFile(zone, index):
            try:
                with open(f"temple/{zone}{index}.json") as _f:
                    return True
            except FileNotFoundError:
                return False

        for temple in temples:
            value = temples[temple]
            for index in value:
                if not checkJsonFile(temple, index):
                    downloadWithTemple(temple, index)

    def test_nft_itemList(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DoPWjW1ERyLMcr8fmTEhm799ucVg_CTYJydTvmPQaNQ"
        )
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.webNftItemList()

    def test_nft_drago(self):
        import os

        import requests

        _lock = threading.BoundedSemaphore(10)
        baseUrl = "https://lok-nft.leagueofkingdoms.com/api/card/drago/"
        downloadNows = []

        def getDrago(id):
            # lock.acquire()
            url = baseUrl + str(id)
            try:
                fileName = "./nft/drago%d.png" % (id)
                if os.path.exists(fileName):
                    print(f"{fileName} 已存在")
                    return

                # ,proxies={'https': f'socks5://127.0.0.1:1086'})
                res = requests.get(url)
                if res.status_code == 200:
                    if res.text[0] != "{":
                        downloadNows.append(id)
                        with open(fileName, "wb") as f:
                            f.write(res.content)
            except Exception as e:
                print(f"{url} 请求失败:{e}")
                pass
            # finally:
            #     lock.release()

        # threads = []
        pool = ThreadPoolExecutor(max_workers=20)
        for i in range(8000, 10001):
            pool.submit(getDrago, 10000 + i)
            # t = threading.Thread(target=getDrago, args=(10000+i,))
            # threads.append(t)
            # t.start()
        pool.shutdown(wait=True)
        # for t in threads:
        #     t.join()
        print(f"本轮下载成功{len(downloadNows)},详情:\n{downloadNows}")
        # getDrago(10001)

    def test_nft_egg(self):
        import os

        import requests

        os.makedirs("egg", exist_ok=True)

        # lock = threading.BoundedSemaphore(10)
        baseUrl = "https://lok-nft.leagueofkingdoms.com/api/card/egg/"
        downloadNows = []

        def getDrago(id):
            # lock.acquire()
            url = baseUrl + str(id)
            try:
                fileName = "./egg/egg%d.png" % (id)
                if os.path.exists(fileName):
                    print(f"{fileName} 已存在")
                    return

                # ,proxies={'https': f'socks5://127.0.0.1:1086'})
                res = requests.get(url)
                if res.status_code == 200:
                    if res.text[0] != "{":
                        downloadNows.append(id)
                        with open(fileName, "wb") as f:
                            f.write(res.content)
            except Exception as e:
                print(f"{url} 请求失败:{e}")
                pass
            # finally:
            #     lock.release()

        # threads = []
        pool = ThreadPoolExecutor(max_workers=10)
        for i in range(8000, 10001):
            pool.submit(getDrago, 10000 + i)
            # t = threading.Thread(target=getDrago, args=(10000+i,))
            # threads.append(t)
            # t.start()
        pool.shutdown(wait=True)
        # for t in threads:
        #     t.join()
        print(f"本轮下载成功{len(downloadNows)},详情:\n{downloadNows}")
        # getDrago(10001)

    def test_nft_drago_detail(self):
        import os

        import requests

        os.makedirs("drago", exist_ok=True)

        # lock = threading.BoundedSemaphore(1)
        baseUrl = "https://lok-nft.leagueofkingdoms.com/api/market/detail"
        downloadNows = []

        def getDrago(id):
            # lock.acquire()
            url = baseUrl
            try:
                fileName = "./drago/drago%d.json" % (id)
                if os.path.exists(fileName):
                    print(f"{fileName} 已存在")
                    return

                # ,proxies={'https': f'socks5://127.0.0.1:1086'})
                res = requests.post(url, json={"tokenId": f"{id}"})
                if res.status_code == 200:
                    resJson = res.json()
                    if resJson["result"]:
                        downloadNows.append(id)
                        with open(fileName, "wb") as f:
                            f.write(res.content)
            except Exception as e:
                print(f"{url} 请求失败:{e}")
                pass
            # finally:
            #     lock.release()

        # threads = []
        pool = ThreadPoolExecutor(max_workers=30)
        for i in tqdm(range(1, 5800)):
            pool.submit(getDrago, 10000 + i)

        for i in tqdm(range(8000, 36201)):
            pool.submit(getDrago, 10000 + i)
            # t = threading.Thread(target=getDrago, args=(10000+i,))
            # threads.append(t)
            # t.start()
        pool.shutdown(wait=True)
        # for t in threads:
        #     t.join()
        print(f"本轮下载成功{len(downloadNows)},详情:\n{downloadNows}")
        # getDrago(10001)

    def test_reload_drago(self):
        import requests

        baseUrl = "https://lok-nft.leagueofkingdoms.com/api/market/detail"
        downloadNows = []

        def getDrago(id):
            # lock.acquire()
            url = baseUrl
            try:
                fileName = "./drago/drago%d.json" % (id)
                # ,proxies={'https': f'socks5://127.0.0.1:1086'})
                res = requests.post(url, json={"tokenId": f"{id}"})
                if res.status_code == 200:
                    resJson = res.json()
                    if resJson["result"]:
                        downloadNows.append(id)
                        with open(fileName, "wb") as f:
                            f.write(res.content)
            except Exception as e:
                print(f"{url} 请求失败:{e}")
                pass
            # finally:
            #     lock.release()

        # threads = []
        pool = ThreadPoolExecutor(max_workers=30)
        files = os.listdir("./drago/")
        for file in files:
            # newIds = []
            with open(f"./drago/{file}", "r", encoding="utf-8") as f:
                # codeMap = {}
                data = json.load(f)
                drago = data["drago"]
                grade = drago["grade"]
                fusion = drago["fusion"]
                tokenId = drago["tokenId"]
                # stats = data["stats"]
                # doubleString = f"{tokenId}"
                # doubleNum = 0
                if grade > 2 or fusion > 1:
                    pool.submit(getDrago, tokenId)

        pool.shutdown(wait=True)
        print(f"本轮下载成功{len(downloadNows)},详情:\n{downloadNows}")

    def test_load_drago(self):
        import os

        codeNames = {
            12: "出兵上限",
            14: "研究速度",
            19: "团战规模",
            30: "治疗速度",
            31: "死亡率",
            35: "聚集速度",
            36: "建筑速度",
            50: "步兵HP",
            51: "弓兵HP",
            52: "骑兵HP",
            53: "步兵防御",
            54: "弓兵防御",
            55: "骑兵防御",
            56: "步兵攻击",
            57: "弓兵攻击",
            58: "骑兵攻击",
            59: "步兵速度",
            60: "弓兵速度",
            61: "骑兵速度",
            62: "步兵装载",
            63: "步兵装载",
            64: "步兵装载",
            109: "资源生产",
            119: "资源上限",
            133: "单步HP",
            134: "单步防御",
            135: "单步攻击",
            136: "单弓HP",
            137: "单弓防御",
            138: "单弓攻击",
            139: "单骑HP",
            140: "单骑防御",
            141: "单骑攻击",
            157: "单骑速度",
            160: "全兵种HP",
            161: "全兵种防御",
            162: "全兵种攻击",
            179: "防御HP",
            180: "防御防御",
            181: "防御攻击",
            204: "单弓速度",
            205: "单步速度",
            206: "开团HP",
            207: "开团防御",
            208: "开团攻击",
        }
        files = os.listdir("./drago/")
        # u1=UserInfo()
        # colorsFunc = [u1.yellowString,u1.blueString,u1.redString]
        highCodes = []
        doubleCodes = []
        legendDragos = []
        details = []

        for file in files:
            # newIds = (
            #     []
            # )  # [10038, 10057, 10153, 10873, 10880, 10948, 11448, 11728, 11688, 11848, 12067, 12197, 12225, 12231, 12229, 12239, 12238, 12240, 12382, 12593, 12673, 12708, 12750, 13364, 13393, 13395, 13455, 13421, 13491, 13577, 13624, 13622, 13643, 13636, 13652, 13669, 13680, 13708, 13665, 13694, 13630, 13700, 13719, 13761, 13770, 13779, 13775, 13814, 13929, 13941, 13947, 14323, 14421, 14423, 14422, 14448, 14501, 14511, 14424, 14759, 14773, 14824, 14826, 14825, 14823, 14827, 14828, 14829, 14830, 14818, 14817, 14831, 14986, 15100, 15102, 15108, 15171, 15235, 15315, 15491, 15440, 15450, 15490, 15520, 15578, 15751, 15769, 15787, 18008, 18009, 18010, 18016, 18019, 18031, 18036, 18035, 18054, 18053, 18056, 18064, 18069, 18071, 18073, 18075, 18076, 18086, 18085, 18091, 18092, 18095, 18100, 18120, 18118, 18130, 18153, 18175, 18191, 18202, 18210, 18211, 18221, 18233, 18234, 18241, 18240, 18250, 18254, 18260, 18266, 18269, 18272, 18273, 18274, 18275, 18279, 18281, 18289, 18311, 18312, 18310, 18321, 18327, 18336, 18337, 18351, 18365, 18367, 18375, 18405, 18414, 18423, 18427, 18432, 18437, 18439, 18436, 18438, 18444, 18447, 18459, 18467, 18476, 18478, 18483, 18489, 18487, 18493, 18540, 18552, 18556, 18576, 18583, 18595, 18609, 18601, 18600, 18621, 18626, 18635, 18644, 18660, 18656, 18661, 18654, 18666, 18679, 18694, 18693, 18695, 18709, 18713, 18714, 18723, 18721, 18726, 18730, 18727, 18732, 18737, 18744, 18745, 18750, 18756, 18775, 18784, 18789, 18807, 18820, 18823, 18830, 18829, 18825, 18834, 18861, 18875, 18873, 18877, 18881, 18876, 18897, 18889, 18901, 18902, 18912, 18927, 18923, 18932, 18937, 18951, 18979, 18980, 18982, 18998, 19014, 19015, 19028, 19029, 19030, 19031, 19035, 19043, 19050, 19047, 19053, 19055, 19056, 19066, 19067, 19083, 19118, 19125, 19156, 19158, 19172, 19180, 19185, 19187, 19198, 19199, 19221, 19226, 19247, 19249, 19257, 19293, 19298, 19320, 19343, 19346, 19363, 19387, 19394, 19395, 19406, 19410, 19419, 19426, 19424, 19431, 19435, 19457, 19460, 19458, 19469, 19481, 19483, 19495, 19503, 19515, 19532, 19539, 19548, 19553, 19556, 19554, 19563, 19559, 19577, 19594, 19609, 19621, 19622, 19624, 19626, 19631, 19644, 19650, 19647, 19666, 19658, 19664, 19679, 19678, 19682, 19683, 19684, 19690, 19691, 19696, 19698, 19702, 19705, 19710, 19717, 19730, 19783, 19790, 19822, 19849, 19873, 19879, 19890, 19897, 19905, 19934, 19952, 19966, 19965, 19986, 19993]
            # if int(file[5:10]) not in newIds:
            #     continue
            with open(f"./drago/{file}", "r", encoding="utf-8") as f:
                codeMap = {}
                data = json.load(f)
                drago = data["drago"]
                grade = drago["grade"]
                fusion = drago["fusion"]
                tokenId = drago["tokenId"]
                stats = data["stats"]
                # doubleString = f"{tokenId}"
                # doubleNum = 0
                if grade > 2:
                    legendDragos.append(f"{tokenId} {grade} {fusion}")
                for stat in stats:
                    ability = stat["ability"]
                    code = ability["code"]
                    if codeMap.get(code):
                        codeMap[code] += 1
                    else:
                        codeMap[code] = 1
                market = drago.get("market", None)
                priceStr = "0 N"
                if market:
                    owner = drago["owner"]
                    marketOwner = market["owner"]
                    if owner == marketOwner:
                        price = market["price"]
                        token = int(market["token"])
                        priceStr = price
                        if token == 1:
                            priceStr += " E"
                        elif token == 2:
                            priceStr += " L"
                        else:
                            priceStr += " N"

                abilitys = sorted(codeMap.items(), key=lambda x: x[1], reverse=True)
                abString = " ".join(
                    [f"{codeNames.get(key,key)}x{value}" for key, value in abilitys]
                )
                s = f"{tokenId} {grade} {fusion} {priceStr} {abString}"
                # print(s)
                details.append(s)
                # for key,value in codeMap.items():
                #     if value > 1:
                #         doubleNum += 1
                #         codeName = codeNames.get(key,key)
                #         doubleString += f' {codeName}x{value}'
                #         s = f'{tokenId} {codeName} {value}'
                #         if value > 2:
                #             highCodes.append(s)
                #             # s = colorsFunc[value - 2](s)
                #         print(s)

                # if doubleNum > 1:
                #     doubleCodes.append(doubleString)

        def sortList(items):
            highMap = {}
            for s in items:
                highMap[s[:6]] = s
            codes = list(highMap.keys())
            codes.sort()
            lastList = []
            for code in codes:
                lastList.append(highMap[code])
            return lastList

        s = "\n".join(sortList(highCodes))
        print(f"高频率code:\n{s}")

        s = "\n".join(sortList(legendDragos))
        print(f"传奇drago:\n{s}")

        s = "\n".join(sortList(doubleCodes))
        print(f"多重字段drago:\n{s}")

        with open("drago.txt", "a+") as f:
            f.writelines([f"{v}\n" for v in details])

    def test_get_drago_detail(self):
        dragos = [
            "40050",
            "32141",
            "41620",
            "32928",
            "35455",
            "36748",
            "25216",
            "25200",
            "25566",
            "25563",
            "25562",
            "25415",
            "35669",
            "40706",
            "23034",
            "42122",
            "32673",
            "36455",
            "28746",
            "25336",
            "38506",
            "41263",
            "31281",
            "42410",
            "42808",
            "32887",
            "25342",
            "29477",
            "41051",
            "41050",
            "41047",
            "41045",
            "27204",
            "34084",
            "42683",
            "40385",
            "30179",
            "43028",
            "43029",
            "43101",
            "27599",
            "42066",
            "29561",
            "23646",
            "29559",
            "29560",
            "39969",
            "39970",
            "35808",
            "35861",
            "25544",
            "39752",
            "29222",
            "28048",
            "28169",
            "35779",
            "42394",
            "40512",
            "36768",
            "39954",
            "42204",
            "41713",
            "39571",
            "22252",
            "37764",
            "30977",
            "30812",
            "31605",
            "37170",
            "31867",
            "21413",
            "23087",
            "25348",
            "32643",
            "42254",
            "40779",
            "40880",
            "28686",
            "39804",
            "36739",
        ]
        owners = {}
        for dragoId in dragos:
            with open(f"./drago/drago{dragoId}.json", "r", encoding="utf-8") as f:
                data = json.load(f)
                drago = data["drago"]
                owner = drago["owner"]
                if drago.get("rent"):
                    rent = drago["rent"]
                    fromOwner = rent.get("from")
                    if fromOwner:
                        owner = fromOwner

                if owners.get(owner):
                    owners[owner].append(dragoId)
                else:
                    owners[owner] = [dragoId]

        from Api.FlaskHelper import getTofunftSellOrderData, requestDragoInventory

        for owner, dragos in owners.items():
            myDragos = requestDragoInventory(owner)
            if myDragos:
                for v in myDragos:
                    # filter = v['filter']
                    # parts = filter['parts']
                    # dark = parts['dark']
                    # if dark == 7:
                    tokenId = v["tokenId"]
                    level = v["level"]
                    grade = v["grade"]
                    if str(tokenId) in dragos:
                        print(f"编号: {tokenId} 等级: {level} Legend: {grade} \n")

    def test_MiningWorkman(self):
        from FlaskApp.Models.MiningWorkMan import MiningWorkMan

        man: MiningWorkMan = MiningWorkMan.query.all()[0]
        print(man.loadParams())

    def test_mail_read(self):
        from datetime import datetime

        user = self.setupAccount("71314e86-fb54-674c-925d-2fa15c3017bc")
        # user.login()
        logs = []
        with open("a61d288f2f8777c3f5d77a5d4_VEGETA.log", "r") as f:
            j = f.read()
            logs = json.loads(j)
        # logs = [{"param":{"mailId":"659f6daebdd64447b14a91f8"}}]
        for v in logs:
            param = v.get("param", {})
            mailId = param.get("mailId")
            if mailId:
                mail = user.mailRead(mailId)
                param = mail.get("param", {})
                battleResult = param[1]["battleResult"]
                before = battleResult["before"]
                if len(before) == 1:
                    logger.info(json.dumps(mail))
            time.sleep(2)

    def test_chatLog(self):
        from datetime import datetime

        chatId = "a61d288f2f8777c3f5d77a5d4"  # LGN1
        chatId = "a61e0854ec2da8511aa78fcff"  # LGN3
        # chatId = 'w24'
        startTime = datetime.strptime("2024-01-11", "%Y-%m-%d")
        user = self.setupAccount("71314e86-fb54-674c-925d-2fa15c3017bc")
        user.initLog(needTestS5=False)
        logs = user.chatLogs(chatId)
        needMore = True
        while needMore:
            log = logs[-1]
            registered = log["registered"]
            date_obj = datetime.strptime(registered, "%Y-%m-%dT%H:%M:%S.%fZ")
            if date_obj < startTime:
                break
            logger.info(f"当前日志数量:{len(logs)} 日期:{registered}")
            lastLogId = log["_id"]
            oldLogs = user.chatLogs(chatId, lastLogId)

            if oldLogs:
                logs += oldLogs
                time.sleep(3)
        filterLogs = list(filter(lambda log: log["from"] == "VEGETA", logs))
        print(json.dumps(filterLogs))
        print(json.dumps([log["text"] for log in logs]))

    def test_joinAllianceBattle(self):
        user = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.n2o9xFXgb8D557fJlKIjXRTqu0Jk68ArYQnOvYraBPE"
        )
        user.loadEmailWithToken()
        if user.login():
            time.sleep(2)
            user.wsWithKingdomApp()
            time.sleep(2)
            while True:
                user.joinAllianceBattle(warJoin=True)
                time.sleep(10)

    def test_warringInfo(self):
        user = UserInfo(
            "<EMAIL>", "Bra05cud", socks5=random.choice(loadS5List())
        )
        # user = self.setupAccount('c1ee0abd-52a8-4c12-a26b-ec03b48d9677')
        # user = UserInfo("<EMAIL>",token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c1M2FGnK4VNCXKYT6Cu8kmyXqLuyMKMmBbEXG5d31_s")
        # user = UserInfo("<EMAIL>",'a123a123',socks5=random.choice(loadS5List()))
        # user.initLog(needTestS5=False)
        # itemCode = ********
        # print(json.dumps(res))
        # items = [enum.ITEM_CODE_MYTHIC_TROOP_ATTACK,enum.ITEM_CODE_MYTHIC_TROOP_DEFINE,enum.ITEM_CODE_MYTHIC_TROOP_HP]
        # for item in items:
        #     res = user.itemBuyUse(item, 10)
        #     print(json.dumps(res))
        #     time.sleep(3)
        # return
        if user.login():
            user.wsWithKingdomApp()
            print(user.itemBuyUse(enum.ITEM_CODE_MYTHIC_TROOP_ATTACK, 1))
            return
            crystal = user.crystal - 150000
            if crystal > 0:
                num = crystal // 2500
                print(f"当前砖石:{crystal} 可购买:{num}")
                time.sleep(3)
                print(user.itemBuyUse(enum.ITEM_CODE_SKIN_BOX, num))

    def test_shrineTitle(self):
        user = self.setupAccount("9b349ea3-38a2-5276-86e4-8a73e48e6a26")
        if user.login():
            res = user.shrineTitle()
            print(json.dumps(res))

    def test_find(self):
        from Unit.UserInfoHelper import searchAll

        users = self.loadGuestAccount()

        [u.initLog(needTestS5=False) for u in users]
        [u.login() for u in users]
        tokens = [u.token for u in users]

        data = searchAll(
            tokens,
            [20, 1024, 1024],
            codes=[enum.OBJECT_CODE_KINGDOM],
            zones=list(range(4096)),
        )
        infos = data.get(enum.OBJECT_CODE_KINGDOM)
        if infos:
            kingdoms = []
            for info in infos:
                occupied = info.get("occupied")
                if occupied:
                    id = occupied["id"]
                    worldId = occupied["worldId"]
                    if worldId != 20:
                        continue
                    allianceTag = occupied.get("allianceTag", "无")
                    name = occupied["name"]
                    level = info["level"]
                    loc = ",".join([str(v) for v in info["loc"][1:]])
                    kingdom = f"{id} [{allianceTag}] {name}[{worldId}] {level} {loc}\n"
                    kingdoms.append(kingdom)
            with open(f"./tmpaccount/{todayStr()}-{int(time.time())}.txt", "a+") as f:
                f.writelines(kingdoms)
        # print(data)

    def test_gusetUser(self):
        redisHelper.clearSocks5()
        # redisHelper.clearTokens()
        users = self.loadGuestAccount()
        # users[0].login()
        # 使用线程池启动
        with ThreadPoolExecutor(max_workers=6) as pool:
            pool.map(lambda u: u.login(), users)
            pool.shutdown()
        from Unit.UserInfoHelper import searchAll

        print("开始搜索")
        # codes = [enum.OBJECT_CODE_MAGODA,enum.OBJECT_CODE_SPARTOI]
        codes = [enum.OBJECT_CODE_DEATHKAR, enum.OBJECT_CODE_RED_DRAGON]
        levels = {
            enum.OBJECT_CODE_DEATHKAR: 4,
            enum.OBJECT_CODE_RED_DRAGON: 1,
        }
        datas = searchAll(
            [u.token for u in users],
            [20, 1024, 1024],
            codes=codes,
            zones=list(range(4096)),
        )
        if datas:
            for code in datas:
                data = datas[code]
                level = levels.get(code, 4)
                # data = datas[codes[0]]
                if data:
                    values = filter(
                        lambda x: x["level"] >= level and x["hidden"] is False, data
                    )
                    logger.info("\n".join([f'{x["loc"]} {x["level"]}' for x in values]))
                logger.info(f"{code} 总数据:{len(data)}")
        # [u.login() for u in users]

    def test_oneSearch(self):
        from Unit.UserInfoHelper import searchAll

        users = self.loadGuestAccount()
        user = users[0]
        splitNum = len(crystalLands) // 8
        os.environ["LEAGUEDEBUG"] = "1"
        # user.initLog(needTestS5=False)
        user.login()
        searchAll(
            [user.token],
            [2, 1024, 1024],
            zones=crystalLands[:splitNum],
            logInfo=logger.info,
        )

    def test_mulit_search(self):
        from Unit.LandZoneUnit import (
            AllZoneOfPolygon,
            NewPoint,
            NewPolygon,
            isPointOnPolygonEdge,
        )
        from Unit.UserInfoHelper import searchAll

        users = self.loadGuestAccount()

        points = [
            (0, 800),
            (400, 800),
            (400, 400),
            (800, 400),
            (800, 1200),
            (0, 1200),
        ]
        polygon = NewPolygon(points)
        zones = AllZoneOfPolygon(polygon)

        us = users[:4]
        # [v.login() for v in us]
        print(len(zones))
        lastSps = []
        currentMax = 0
        currentStartTime = 0
        while True:
            t1 = time.time()
            res = searchAll(
                [u.token for u in us], [100003, 400, 800], zones=zones, codes=[********]
            )
            t2 = time.time()
            sps = res.get(********, [])
            inRanges = []
            outRanges = []
            for sp in sps:
                loc = sp["loc"]
                level = sp["level"]
                sp["localName"] = f"{level} {loc[1:]}"

                if not sp["hidden"] and isPointOnPolygonEdge(
                    NewPoint(loc[1], loc[2]), polygon
                ):
                    inRanges.append(sp)
                else:
                    outRanges.append(sp)
            if currentMax == 0 or len(lastSps) < len(inRanges):
                currentMax = len(inRanges)
                currentStartTime = time.time()
            lastSps = inRanges
            s = f"搜索耗时:{round(t2 - t1,2)} 统计耗时:{round(time.time() - t2,2)}\n有效数据:\n"
            logger.info(
                f"有效:{len(inRanges)} 无效:{len(outRanges)} 本轮消耗:{currentMax - len(inRanges)} 本轮耗时:{round((time.time() - currentStartTime) / 60.0,2)}"
            )
            for sp in inRanges:
                s += f"{sp['localName']}\n"

            s += "区域外:\n"
            for sp in outRanges:
                s += f"{sp['localName']}\n"
            print(s)
            [time.sleep(1) for v in range(60)]

    def test_pkg_claim(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PY6IsfKdpJq3rBzGaq2tVN_Zt2_zA-AmoSc6kuNWadQ"
        user = UserInfo("<EMAIL>", token=token)
        user.initLog(needTestS5=False)
        # print(user.pkgDailyFreeClaim("64a3616c77941e072c3a57c4"))
        print(user.tryClaimVip())

    def test_miningHelper(self):
        from Unit.UserInfoHelper import MiningHelper

        u1 = UserInfo()
        u1.kingdomId = "a"
        a = MiningHelper(u1)
        a.getLocalRegisterUser()

    def test_chests(self):
        u1 = UserInfo(
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.UpAGwqWySt3KlBzZndEkTKJHfp-fzlYn9svL5XlOVtg"
        )
        u1.loadEmailWithToken()
        if u1.login():
            # u1.tryFreeChests()
            print(u1.claimDragoMint(18009))

    def test_dsaVipList(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.thuqcqT6V2-bdI_00RW9tHiGrc3QhgcupwxjChXdQlk"
        u1 = UserInfo(token=token, socks5="085:20220802@************:45038")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        print(u1.dsavipshopList())

    def test_tryRoulette(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.A8WYSc4QBcj0i6SJ6oW9H_n0VGCtuqf-USQU61ucHFM"
        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.debugToConsole()
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        u1.rouletteEvent = {
            "_id": "66331f02898b9f0341577378",
            "isOpen": True,
            "startDate": "2024-05-03T00:00:00.000Z",
            "endDate": "2024-05-06T00:00:00.000Z",
            "time": 259200,
        }
        u1.requestConnect()
        u1.tryRoulette_2023()

    def test_buildTroop(self):
        res = {
            "result": True,
            "distance": 320,
            "troops": [
                {"_id": "61963c08f1ea461ace43a108", "code": ********, "amount": 40000},
                {"_id": "61963e6dc6cf5d7c023cb69b", "code": ********, "amount": 306500},
                {"_id": "61d3de54041df530542abe29", "code": 50100204, "amount": 43809},
                {"_id": "61dbf123cc8da951fb18501f", "code": 50100104, "amount": 5195},
                {"_id": "61e60b382db2de190a03f1ff", "code": 50100304, "amount": 139641},
            ],
            "saveTroops": [
                [
                    {
                        "_id": "6460df223871e811d011ea93",
                        "code": 50100304,
                        "select": 0,
                        "amount": 20000,
                    }
                ],
                [
                    {
                        "_id": "644b6e93efe0a472153c7054",
                        "code": 50100304,
                        "select": 0,
                        "amount": 50000,
                    }
                ],
                [
                    {
                        "_id": "644b6eaae30a2d2f326ad7de",
                        "code": 50100304,
                        "select": 0,
                        "amount": 15000,
                    }
                ],
                [
                    {
                        "_id": "644a2d0065e97a2f7ae23039",
                        "code": 50100304,
                        "select": 0,
                        "amount": 500,
                    }
                ],
            ],
            "marchType": 1,
            "fo": {
                "_id": "656933022b598e0845261227",
                "loc": [20, 1430, 1062],
                "level": 2,
                "code": 20100105,
                "param": {"value": 100},
                "state": 1,
                "expired": "2023-12-02T20:56:42.341Z",
            },
            "numMarch": 3,
            "numMaxTroops": -1,
            "currentTroops": -1,
            "maxTroops": -1,
        }
        u1 = UserInfo()
        u1.kingdomTroops = res.get("troops")
        print(u1.buildMarchTroopFromKingdom(50100306, 200))

    def test_autoLvel(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.4-ZCNTu8MOzk5z6Zykt9dzrsfNXscrLtTQLPJ499Dkg"
        socks5 = "jj:jj@************:20316"
        u1 = UserInfo("ansdflsdfj", token=token, socks5=socks5)
        u1.debugToConsole()
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        if u1.enter():
            # u1.wsWithKingdomApp()
            time.sleep(5)
            while True:
                u1.autoLevelTo35()

    def test_bu(self):
        from Api.User.AutoOptions import getBuildInfo

        getBuildInfo(********, 1)

    def test_memory(self):
        beforeMem = memory_usage()
        loadBuildJson()
        afterMem = memory_usage()
        print(f"内存消耗:{afterMem - beforeMem}")
        freeBuildJson()
        afterMem2 = memory_usage()
        print(f"内存消耗:{afterMem2 - afterMem}")


class UserInfoTest(unittest.TestCase):
    def setUp(self):
        print("\n")
        self.u1 = UserInfo(
            # "<EMAIL>","111111",
            userKey="db20a17f-bca0-4a12-9e9b-0eca99a38326",
            #  socks5="a:b@***********:33095"
        )
        # self.u1 = UserInfo("<EMAIL>","111111",socks5="127.0.0.1:1086",token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWI0OTY4MjRkMWM0ZTNiODJiNDQxNzYiLCJraW5nZG9tSWQiOiI2MWI0YmZhZDE0MjYwNDNiOWQxYjdmNGUiLCJ0aW1lIjoxNjQxMDA3NzQxNjk3LCJpYXQiOjE2NDEwMDc3NDEsImV4cCI6MTY0MTYxMjU0MSwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.sVVZVLLL50C-KJFRssxwd7NH5B44BYdp4vXLEG-lgQE")
        self.loop = asyncio.get_event_loop()

        # if not self.u1.isLogin:
        #     self.test_login()
        # if not self.u1.hasInit:
        #     self.runTask(self.u1.initLog())

    def test_joinWarfareAllianceBattle(self):
        u1 = UserInfo(
            f"{random.randint(1, 99999)}@test.cn",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWMwMGE3OTY1YTY1ODE1MDBmNGNhZDkiLCJraW5nZG9tSWQiOiI2MWMwMGE5OTIyYjU2ZDE1NDFlZmU5ZjQiLCJ2ZXJzaW9uIjoxNDQxLCJ0aW1lIjoxNjUwNjcxNDMwNzM3LCJpYXQiOjE2NTA2NzE0MzAsImV4cCI6MTY1MTI3NjIzMCwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.MRwPz6gw3cA3AsqiCbrFlbNqAFOhJbn78pOi0AMvPig",
            socks5=random.choice(loadS5List()),
        )
        u1.enter()

        u1.joinWarfareAllianceBattle(66666, "BY-曾赤脚走天涯")

    def test_autoOpenResource(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tALvZ9x9j9wmSgVRrVORPuqWhZGUUckeLxjsIFRC5UI"
        u1 = UserInfo(token=token, socks5="jj:jj@************:20064")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        if u1.enter():
            u1.autoOpenResource()

    def test_caravanList(self):
        u1 = UserInfo(
            f"{random.randint(1, 99999)}@test.cn",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MjM4MGM1ZGI1YjU3MTA3ZGZjNWZlMzUiLCJraW5nZG9tSWQiOiI2MjM4MGM1ZGI1YjU3MTA3ZGZjNWZlMzYiLCJ0aW1lIjoxNjQ4NTA3ODE4NzgxLCJpYXQiOjE2NDg1MDc4MTgsImV4cCI6MTY0OTExMjYxOCwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.PobxpwlyWTNUxAK0oMyYCPzpX7hia-1RKlGsfIFuYB0",
        )
        u1.initLog()
        u1.importantCaravanList()

    def runTask(self, task):
        res = self.loop.run_until_complete(task)
        return res

    def runTasks(self, tasks):
        ress = self.u1.runTasks(tasks)
        self.u1.tmpSum = 0

        def add(u1):
            u1.tmpSum += 1
            return True

        _value = [r.result() and add(self.u1) or False for r in ress[0]]
        self.u1.log(self.u1.tmpSum)

    def test_runCancels(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.BPYdRkpBoO2pL8an9x-xRN0rJ0r5Xv3ldV0_lxa5Whs"

        u1 = UserInfo("any", token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)

        # print(u1.dragoFusion("62a18a97dac142b8bdc4bf8b"))
        # print(u1.dstTake(10000,"6427c5c350302d64a08aaa8e"))
        # print(u1.dragoLairLeave("62a18cb4dac142b8bda0e1b7"))
        u1.runTasks([u1.dragoFusionA("62a18a97dac142b8bdc4bf8b") for i in range(2)])
        # u1.runTasks([u1.buildingUpgradeA(10,1) for i in range(3)])
        print(f"成功执行{u1.bugCount}次")

    def test_get_ip(self):
        res = asyncio.run(self.u1.asyncRequestGet("https://ipinfo.io/json"))
        logger.info(res)

    def test_email_login(self):
        u1 = UserInfo("<EMAIL>", "lxn180808")
        u1.login()

    def test_login(self):
        u1 = UserInfo(userKey="c66f42ed-c0eb-4208-36d8-19bed816ca23", saveSocks5=True)
        s5 = redisHelper.getSocks5Info(u1.key)
        u1.socks5 = s5
        u1.login()

    def test_getRecordCrystal(self):
        from Unit.UserInfoHelper import getRecordCrystals

        sorted_dict = getRecordCrystals()
        # print(sorted_dict.keys())
        print(json.dumps(sorted_dict, ensure_ascii=False))

    def test_linkApple(self):
        # u1 = UserInfo(socks5="127.0.0.1:1087",token='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tgPuI6fInX_OAt5azM5uz19d734Mfwtqn74rTkvWZeE')
        u1 = UserInfo(
            socks5="127.0.0.1:1087",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.yIv0WcBMdYkIkSEsi--YhVxyEdV6CkCVpBM1NzBZelA",
        )
        u1.deviceInfo = {
            "build": "global",
            "OS": "Mac OS X 10_14_5",
            "country": "USA",
            "language": "English",
            "bundle": "",
            "version": "1.1660.143.221",
            "platform": "web",
            "pushId": "",
        }
        u1.initLog(needTestS5=False)
        u1.appleSubId = "91bcdbd002fd4bcdba8090feab609fd0"
        print(u1.appleToken)
        print(u1.linkApple())

    def test_appleLogin(self):
        u1 = UserInfo(
            socks5=random.choice(loadS5List()),
            appleSubId="1dbcdbd002fd4bcdba8090feab609fd0",
        )
        if u1.login():
            print(f"登陆成功{u1.level}")

    def test_saveUser(self):
        from Api.FlaskHelper import requestSaveUser

        print(
            requestSaveUser(
                "fbe46657e2fdebc3c044a7d52d0b5e16",
                '{"OS": "Windows 10", "country": "USA", "language": "Japanese", "version": "1.1660.143.221", "platform": "web", "build": "global", "bundle": ""}',
            )
        )

    def test_updateToken(self):
        from Api.FlaskHelper import updateToken

        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.99ZbiR0gPKCmERH0Y4e9TBmNUnTMa2-XtTuAJFagMvI"
        print(updateToken(token))

    def test_zeroService(self):
        for _ in range(1):
            u1 = UserInfo()
            u1.setupZeroService()
            u1 = None
        time.sleep(2)
        print("end")

    def test_tryGarrison(self):
        u1 = UserInfo()
        u1.setupZeroService()

        def t1():
            while True:
                time.sleep(1)

        t = threading.Thread(target=t1, daemon=True)
        t.start()
        t.join()

    def test_getToken(self):
        from Api.FlaskHelper import requestTokens

        print(requestTokens(2))

    def test_appleIds(self):
        redisHelper.saveAppleSubId("1dbcdbd002fd4bcdba8090feab609fd0")
        print(redisHelper.getAllAppleSubId())

    def test_alliance_my(self):
        res = self.u1.allianceInfoMy()
        self.u1.log(res)

    def test_join_alliance(self):
        self.assertEqual(self.u1.tryJoinAlliance(), True)

    def test_allianceShop(self):
        self.u1.allianceHelpAll()
        self.u1.tryAllianceDonate()

    def test_daliy_task(self):
        u1 = UserInfo(userKey="9ae1360f-018a-e04d-10d4-977d15e2901c", saveSocks5=True)
        if u1.login():
            u1.tryHarvestAll()
            u1.tryClaimDaily()
        u1.tryClaimDaily()
        print(u1.dailyPoint)

    def test_runTasks(self):
        tasks = [asyncA(n) for n in range(10)]
        res = self.u1.runTasks(tasks)
        print(res)

    def test_runTaskForRequest(self):
        tasks = [asyncB(self.u1) for n in range(5)]
        res = self.u1.runTasks(tasks)
        print(res)

    def test_runTaskForRequestRent(self):
        from Api.FlaskHelper import requestRentClaimA

        tasks = [
            requestRentClaimA("0x606bcabe7acf4c9c5c1a7f4a059cfcd3f15fdbed")
            for n in range(10)
        ]
        res = self.u1.runTasks(tasks)
        print(res)

    async def test_get_mail(self):
        tasks = [self.u1.mailClaim("61cf86d001ef344bf5bc8722") for n in range(10)]
        await self.u1.runTasks(tasks)

    def test_mailcheck(self):
        u1 = UserInfo(
            "897e556b-79d7-09f4-e883-d833bc6f5571",
            userKey="897e556b-79d7-09f4-e883-d833bc6f5571",
            saveSocks5=True,
        )
        if u1.login():
            u1.checkDsaMail()

    def test_buy_vipshop(self):
        tasks = [self.u1.vipshopBuyA(10101049, 20) for n in range(10)]
        self.u1.runTasks(tasks)
        self.u1.log(f"vip shop {self.u1.bugCount}")
        # self.u1.runTasks([self.u1.vipInfoA()])

    def test_vipList(self):
        s5List = loadS5List()
        u1 = UserInfo(
            "<EMAIL>",
            "wxl668899",
            socks5=random.choice(s5List),
        )
        if u1.login():
            u1.wsWithKingdomApp()
            u1.autoBuyVipShopAll()

    def test_autoOpenTreasureBox(self):
        s5List = loadS5List()
        u1 = UserInfo(
            "<EMAIL>",
            "wxl668899",
            socks5=random.choice(s5List),
        )
        if u1.login():
            u1.wsWithKingdomApp()
            u1.autoOpenTreasureBox()

    def test_treasureList(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.oa9npa996sCVwNcYZiuVO21OUWppB3pLq20vzWtOzXw"
        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)

        print(json.dumps(u1.treasureList(), ensure_ascii=False))

    def test_autoTreasure(self):
        token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qfEKLokB1uE5jY2NzMSYrpupy707jZbqaXJjnfquqi0"

        u1 = UserInfo(token=token, socks5="127.0.0.1:1087")
        u1.loadEmailWithToken()
        u1.initLog(needTestS5=False)
        from Api.User.AutoOptions import TreasureAttackPages, TreasureCollectPages

        u1.autoTreasures(0, TreasureAttackPages)

    def test_pid(self):
        print(os.getpid())

    async def test_use_item(self):
        tasks = [self.u1.itemUse(10101014, 5) for n in range(10)]
        await self.runTasks(tasks)

    def test_buys(self):
        u1 = UserInfo(
            "test",
            socks5="a:b@***********:33079",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWJiY2NiNGU4YzI2ZDE4OGM1N2I3MWMiLCJraW5nZG9tSWQiOiI2MWMzMzAyYTQ2MTIwZDEwYTFjNGI2OTUiLCJ0aW1lIjoxNjQxMTM0MTYzMDU1LCJpYXQiOjE2NDExMzQxNjMsImV4cCI6MTY0MTczODk2MywiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.kFzKYskUWgYG5AJU9t8es4tkmY1_DNEc6m2D5lnUyMQ",
        )
        u1.initLog()
        tasks = [u1.vipshopBuyA(10101035, 20) for n in range(15)]
        self.loop.run_until_complete(asyncio.wait(tasks))
        # await self.loop.run_until_complete(tasks)
        # u1.runTasks(tasks)

    def darkedge_init(self):
        _s5List = loadS5List()
        u1 = UserInfo(
            "<EMAIL>",
            "111111",
            #   socks5=random.choice(s5List),
            socks5="127.0.0.1:1086",
            token="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2MWMwMGE3OTY1YTY1ODE1MDBmNGNhZDkiLCJraW5nZG9tSWQiOiI2MWMwMGE5OTIyYjU2ZDE1NDFlZmU5ZjQiLCJ0aW1lIjoxNjQxOTE3NjQ4NTE0LCJpYXQiOjE2NDE5MTc2NDgsImV4cCI6MTY0MjUyMjQ0OCwiaXNzIjoibm9kZ2FtZXMuY29tIiwic3ViIjoidXNlckluZm8ifQ.DCCUsHfigALFAh7bIHNEkWtrA14mgRLg1EdB2MMzq7g",
        )
        if u1.login():
            u1.enter()

        self.u1 = u1

    def test_AttackMonster(self):
        self.darkedge_init()
        u1 = self.u1
        while u1.actionPoint > 10:
            res = u1.tryAttackMonster(20200101, maxDistance=100)
            u1.randomSleep(res and 30 or 60)

    def test_tryGathering(self):
        self.darkedge_init()
        u1 = self.u1
        while True:
            u1.tryGathering()

    def test_loginfo(self):
        import re

        reStr = "api:.+ res:"
        apis = {}
        with open("log.log", "r") as f:
            for line in f.readlines():
                res = re.search(reStr, line)
                if res:
                    api = line[res.start() + 4 : res.end() - 5]
                    if api not in apis:
                        apis[api] = 1
                    else:
                        apis[api] += 1
        for api in apis:
            logger.info(f"{api} {apis[api]}")

    def test_miningClass(self):
        from Api.FlaskHelper import requestToken
        from Unit.UserInfoHelper import MiningHelper

        # token = requestToken()
        u1 = UserInfo("test", token="any")
        u1.loc = [21, 1024, 1024]

        helper = MiningHelper(u1)

        helper.run()

    def test_ThreadPoolExecutor_map(self):
        def tryRun(i):
            print(i)
            time.sleep(i)
            if i % 2 == 1:
                return None
            if i == 10:
                return None
            return i

        with ThreadPoolExecutor(max_workers=3) as executor:
            results = executor.map(tryRun, range(1, 5))
            executor.shutdown(wait=True)
            r = list(filter(None, results))
            print(r)

        # res = pool.map(tryRun, [1,2,3,4,5,6,7,8,9,10])
        # pool.shutdown(wait=True)
        # for v in res:
        #     print(v)

    def test_loadCVCShrineIds(self):
        foIds = None
        with open("shrineIds.json") as f:
            txt = f.read()
            foIds = json.loads(txt)
        print(foIds)

    def test_getCVCShrineIds(self):
        from Unit.Redis import redisHelper

        s5List = loadS5List()
        self.u1.socks5 = random.choice(s5List)
        if self.u1.login():
            #     pass
            #     locs =
            locs = []
            x = 200
            y = 200
            while y < 2000:
                if y // 200 % 2 == 0:
                    x = 200
                else:
                    x = 400

                while x < 2000:
                    locs.append([x, y])
                    if y // 200 > 2 and y // 200 < 8:
                        x += 200
                    else:
                        x += 400
                y += 200
            print(locs)
            configs = {}
            for worldId in [100001, 100002, 100003, 100004]:
                for loc in locs:
                    loc = [worldId] + loc
                    locKey = f"foId_{loc[0]}_{loc[1]}_{loc[2]}"
                    foId = redisHelper.get(locKey)
                    if not foId:
                        fields = self.u1.wsGetFields(loc, more=True, power=1)
                        if fields:
                            builds = [20400401, 20600101, 20600102]
                            for key in fields:
                                # if not foId:
                                if key in builds or key >= 20700101:
                                    values = fields[key]
                                    for value in values:
                                        if loc == value.get("loc"):
                                            foId = value.get("_id")
                                            redisHelper.set(locKey, foId)
                                            print(f"{loc} {foId}")
                                            configs[locKey] = foId
                                            break
                    else:
                        configs[locKey] = foId
            print(configs)
            print(json.dumps(configs))

    def test_get_drago_ap(self):
        user = UserInfo(
            userKey="9e308ea4-602d-eb3e-672c-7dc8baf932ef",
            socks5=random.choice(loadS5List()),
            saveSocks5=True,
        )
        if user.login():
            if user.tryLinkWallet(
                "0x15d0183facb3d29883c3a0455d7410ba3cdd9beb8fc9cb79b6ebbde1bca3bb0d"
            ):
                dragos = user.nftDragoList()
                dragoId = dragos[0].get("_id")
                user.dragoLairLeave(dragoId)
                user.dragoLairJoin(dragoId)
                user.log(f"{user.dragoLairList()}")
                for _i in range(25):
                    user.enter()
                    user.log(f"龙ap:{user.dragoActionPoint}")
                    if user.dragoActionPoint > 0:
                        user.log("成功")
                        break
                    else:
                        user.randomSleep(30, 60, msg="傻等")

    def test_get_drago_list(self):
        from Api.FlaskHelper import getTofunftSellOrderData, requestDragoInventory

        myDragos = requestDragoInventory("******************************************")

        if myDragos:
            for v in myDragos:
                # filter = v['filter']
                # parts = filter['parts']
                # dark = parts['dark']
                # if dark == 7:
                tokenId = v["tokenId"]
                level = v["level"]
                grade = v["grade"]
                if level > 20:
                    print(f"编号: {tokenId} 等级: {level} 传奇: {grade} \n")
                    # print(getTofunftSellOrderData(tokenId))

    def test_getDragoInventory(self):
        from Api.FlaskHelper import requestDragoInventory

        res = requestDragoInventory("0x3BE7830AFD1Aa242757BD5d7cf29e9887F7FbFFD")
        for drago in res:
            rent = drago.get("rent")
            tokenId = drago.get("tokenId")
            if rent:
                print(f'{tokenId} {rent.get("to")}')
        # print(res)

    def test_append_write(self):
        from Unit.FileTool import append_to_file

        append_to_file("test", "./asdfasdf.txt")

    def test_logwrite(self):
        from Api.FlaskHelper import requestLogWrite

        requestLogWrite("test", "aaa", type=3)

    def test_reloadS5(self):
        from Unit.FileTool import reloadSocks5

        reloadSocks5()

    def test_version(self):
        self.u1.login()
        if self.u1.version_live():
            print(self.u1.authCheckVersion())

    def test_redis(self):
        import random

        # 从数据库中获取，key为"coordinates",没有才本地生产
        # redisHelper.removeKey("coordinates")
        t1 = time.time()
        result = redisHelper.smembers("coordinates")
        if not result:
            # 生成10000个不重复的坐标点
            coordinates = set()
            while len(coordinates) < 10000:
                x = random.randint(1000, 1400)
                y = random.randint(1000, 1400)
                coordinates.add(f"20_{x}_{y}")

            # 转换为列表
            result = list(coordinates)
            # 可以打印验证结果
            print(f"生成坐标数量: {len(result)}")

            start_time = time.time()
            redisHelper.removeKey("coordinates")
            redisHelper.sadd("coordinates", *result)
            end_time = time.time()
            print(f"redis写入时间: {end_time - start_time} 秒")
        else:
            t2 = time.time()
            print(f"redis查询时间: {t2 - t1} {len(result)}")

        t1 = time.time()
        for v in random.choices(result, k=500):
            redisHelper.set(v, 1, ex=60 * 15)
        t2 = time.time()
        print(f"redis写入时间: {t2 - t1}")

        t1 = time.time()
        # 使用mget批量获取多个key的值
        values = redisHelper.mget(result)
        # 使用列表推导式过滤出不存在的坐标
        results = [coord for coord, value in zip(result, values) if not value]
        t2 = time.time()
        print(f"redis查询时间: {t2 - t1} {len(results)}")

    def test_batch_redis_ops(self):
        """测试Redis批量操作"""
        # 准备测试数据 - 复杂的JSON数据
        start_time = time.time()
        test_data = {
            f"test_key_{i}": {
                "id": i,
                "name": f"Test Item {i}",
                "description": "This is a long description that helps to make the string longer than 200 characters",
                "attributes": {
                    "color": ["red", "blue", "green"],
                    "size": {"width": 100, "height": 200},
                    "tags": ["test", "sample", "redis", "batch"],
                    "metadata": {
                        "created_at": "2024-01-01",
                        "updated_at": "2024-01-02",
                        "version": 1.0,
                    },
                },
            }
            for i in range(random.randint(500,5000))  # 生成10条测试数据
        }
        data_prep_time = time.time() - start_time
        print(f"数据准备耗时: {data_prep_time:.3f}秒 {len(test_data)}")

        redis_helper = redisHelper

        # 测试批量设置
        set_start = time.time()
        result = redis_helper.batch_set_with_ex(test_data, ex=60)
        set_time = time.time() - set_start
        print(f"批量设置耗时: {set_time:.3f}秒 {len(test_data)}")
        self.assertTrue(result, "批量设置数据应该成功")

        # 测试批量获取
        get_start = time.time()
        keys = list(test_data.keys())
        retrieved_data = redis_helper.batch_get(keys)
        get_time = time.time() - get_start
        print(f"批量获取耗时: {get_time:.3f}秒 {len(retrieved_data)}")

        # 验证数据完整性
        self.assertEqual(
            len(retrieved_data), len(test_data), "获取的数据数量应该与原始数据相同"
        )
        for key in test_data:
            self.assertIn(key, retrieved_data, f"键 {key} 应该存在于获取的数据中")
            self.assertEqual(
                retrieved_data[key], test_data[key], f"键 {key} 的值应该与原始数据相同"
            )

    def test_batch_redis_large_dataset(self):
        """测试Redis大数据集批量操作"""
        import time
        from uuid import uuid4
        # 准备大量测试数据
        data_prep_start = time.time()
        large_test_data = {
            f"large_test_{i}": str(uuid4())
            for i in range(2000)  # 生成20条测试数据
        }
        data_prep_time = time.time() - data_prep_start
        print(f"大数据准备耗时: {data_prep_time:.3f}秒 {len(large_test_data)}")

        redis_helper = redisHelper

        # 测试大数据集批量设置
        set_start = time.time()
        set_result = redis_helper.batch_set_with_ex(
            large_test_data, ex=3600, batch_size=5
        )
        set_time = time.time() - set_start
        print(f"大数据批量设置耗时: {set_time:.3f}秒 {len(large_test_data)}")
        self.assertTrue(set_result, "大数据集批量设置应该成功")

        # 等待一小段时间确保数据写入
        time.sleep(1)

        # 测试大数据集批量获取
        get_start = time.time()
        keys = list(large_test_data.keys())
        retrieved_large_data = redis_helper.batch_get(keys, batch_size=5)
        get_time = time.time() - get_start
        print(f"大数据批量获取耗时: {get_time:.3f}秒 {len(retrieved_large_data)}")

        # 验证数据完整性
        self.assertEqual(
            len(retrieved_large_data),
            len(large_test_data),
            "获取的大数据集数量应该与原始数据相同",
        )

class BarkTest(unittest.TestCase):
    def setUp(self):
        pass

    def test_send(self):
        from Api.BarkApi import barkSendMsg

        a = ["[21,1,1]", "[21,2,2]"]
        barkSendMsg("水晶矿通知", "\n".join(a), "bAzCab9gbvpDza7YYsRjgM")


if __name__ == "__main__":
    unittest.main()


class scheduleTest(unittest.TestCase):
    def setUp(self):
        pass

    def abc(self):
        time.sleep(5)
        logger.info("abc")

    def bbc(self):
        logger.info("start bbc")
        time.sleep(1)
        logger.info("bbc")

    def cbc(self):
        logger.info("start cbc")
        time.sleep(1)
        logger.info("cbc")

    def testLog(self):
        def log():
            logger.info("log")

        import schedule

        schedule.every(1).seconds.do(log)
        while True:
            schedule.run_pending()
            time.sleep(1)
            logger.info("schedule触发Run")

    def testMin(self):
        import schedule

        def scheduleThread1():
            schedule.every(3).seconds.do(self.abc)
            while True:
                # schedule.run_pending()
                # schedule.run_pending()
                time.sleep(1)

        def scheduleThread2():
            schedule.every(1).seconds.do(self.bbc)
            while True:
                schedule.run_pending()
                time.sleep(1)

        threading.Thread(target=scheduleThread1).start()
        threading.Thread(target=scheduleThread2).start()
        while True:
            logger.info("s1")
            # schedule.run_pending()
            time.sleep(1)

    def test_collectCrystalsTask(self):
        config = {
            "email": "<EMAIL>",
            "pwd": None,
            "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.xaQXsnd76nxh9EJ_kqhMydZW-6EhtviJpbTtwa7Z3Kk",
            "socks5": "127.0.01:1087",
            "params": '{"barkKey": "", "maxTroopNum": 2000, "maxCrystalLevel": 7, "minCrystalLevel": 3, "isJoinAllianceBattle": true, "battleTroopNum": 30000, "autoPackage": true, "autoSkill": true, "autoEat": true, "autoBuy": true, "autoLevelUp": false, "autoMinGame": true, "autoGlobalCrystal": true, "autoStartRally": false}',
        }
        collectCrystalsTask(config)

    def test_schedule_day(self):
        import schedule

        schedule.every().day.at("10:31").do(self.bbc)
        schedule.every().day.at("10:31", "Asia/Shanghai").do(self.abc)
        schedule.every().day.at("10:31", "UTC").do(self.cbc)
        while True:
            schedule.run_pending()
            time.sleep(1)


class VaporTest(unittest.TestCase):
    def setUp(self):
        pass

    def testVapor(self):
        from Api.VaporHelper import requestSaveDragoMines, saveDragoMinesInThread

        # requestSaveDragoMines(20,["2_2_2","1_1_1"])
        saveDragoMinesInThread(20, ["2_2_2", "1_1_1"]).join()

    def test_color(self):
        import re

        s = "2024-11-21 18:23:24,556:昵称:Morton 账号:<EMAIL> [] pid:18972 Rulez 攻击 神殿/门 200_800 总兵力:300万 剩余时间104 距离:426 消息:ARCHERS [33m弓兵[0mT7:209869 7.0% [33m弓兵[0mT6:2790131 93.0%"
        s = re.sub(r"\x1b\[31m(.*?)\x1b\[0m", r'<span style="color:red;">\1</span>', s)
        s = re.sub(
            r"\x1b\[33m(.*?)\x1b\[0m", r'<span style="color:green;">\1</span>', s
        )
        s = re.sub(r"\x1b\[34m(.*?)\x1b\[0m", r'<span style="color:blue;">\1</span>', s)
        print(s)


class FlaskTest(unittest.TestCase):
    def setUp(self):
        pass

    def test_stream_bot_work_log(self):
        u1 = UserInfo("test")
        u1.name = "test"
        os.environ["ARK_API_KEY"] = "1fd7acbb-cef1-4f22-a45f-aadf9c4f02e9"
        from volcenginesdkarkruntime import Ark

        client = Ark(
            api_key=os.environ.get("ARK_API_KEY"),
            base_url="https://ark.cn-beijing.volces.com/api/v3",
        )

        # Non-streaming:
        print("----- standard request -----")
        completion = client.chat.completions.create(
            model="ep-20250102165824-89cb9",
            messages=[
                {
                    "role": "system",
                    "content": "你是豆包，是由字节跳动开发的 AI 人工智能助手",
                },
                {
                    "role": "user",
                    "content": "根据当前时间戳生成不同的数据。内容跟时间戳无关。告诉我一句诗，不要超过20个字。",
                },
            ],
            # 免费开启推理会话应用层加密，访问 https://www.volcengine.com/docs/82379/1389905 了解更多
            # extra_headers={'x-is-encrypted': 'true'},
        )
        msg = completion.choices[0].message.content

        print(msg)
        u1.addBotWorkLog(msg)
